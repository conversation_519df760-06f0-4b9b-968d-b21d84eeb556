{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3k8AEwPipuwUgauKLO/KQ0DvRXTLZzY+2yJcVLrgW30=", "__NEXT_PREVIEW_MODE_ID": "42e9db35093521b0b865acc52201887d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8b2fcdf13d3ed140d87ad416a8aa96d18dba6fde10cb5cac22c908f0936ecff8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7a30fbadf3e50f28c44322a1baa2cc36d95c010d9617dcd7479c52beadc67f03"}}}, "instrumentation": null, "functions": {}}