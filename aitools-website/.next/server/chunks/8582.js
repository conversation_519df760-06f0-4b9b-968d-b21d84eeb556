"use strict";exports.id=8582,exports.ids=[8582],exports.modules={11860:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},16023:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33823:(e,t,a)=>{a.d(t,{A:()=>r});var l=a(60687);function r({size:e="md",className:t=""}){return(0,l.jsx)("div",{className:`flex justify-center items-center ${t}`,children:(0,l.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},37360:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},51834:(e,t,a)=>{a.d(t,{A:()=>u});var l=a(60687),r=a(43210),s=a(12340),i=a(77618),n=a(11860),c=a(99270),o=a(37360),d=a(83938);function u({selectedTags:e,onTagsChange:t,maxTags:a=d.z,placeholder:u}){let[m,g]=(0,r.useState)(""),[p,x]=(0,r.useState)(!1),h=(0,s.a8)(),_=(0,i.c3)("common"),v=function(){let e=(0,i.c3)("tags");return d.Az.map(t=>({key:t,label:e(t)}))}();h?.startsWith("/en");let b=l=>{e.includes(l)?t(e.filter(e=>e!==l)):e.length<a&&t([...e,l])},y=a=>{t(e.filter(e=>e!==a))},f=v.filter(t=>t.label.toLowerCase().includes(m.toLowerCase())&&!e.includes(t.key)),E=e=>{let t=v.find(t=>t.key===e);return t?t.label:e};return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:_("select_tags")}),(0,l.jsx)("span",{className:"text-sm text-gray-500",children:_("selected_count",{count:e.length,max:a})})]}),e.length>0&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:_("selected_tags")}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:e.map(e=>(0,l.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[E(e),(0,l.jsx)("button",{type:"button",onClick:()=>y(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,l.jsx)(n.A,{className:"h-3 w-3"})})]},e))})]}),(0,l.jsx)("div",{className:"space-y-3",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("select_tags_max",{max:a})}),(0,l.jsxs)("div",{className:"relative mb-3",children:[(0,l.jsx)("input",{type:"text",placeholder:u||_("search_tags"),value:m,onChange:e=>g(e.target.value),onFocus:()=>x(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,l.jsx)(c.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(p||m)&&(0,l.jsx)("div",{className:"relative",children:(0,l.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:f.length>0?(0,l.jsxs)("div",{className:"p-2",children:[(0,l.jsx)("div",{className:"grid grid-cols-1 gap-1",children:f.map(t=>{let r=e.length>=a;return(0,l.jsx)("button",{type:"button",onClick:()=>{b(t.key),g(""),x(!1)},disabled:r,className:`
                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors
                              ${r?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700"}
                            `,children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(o.A,{className:"h-3 w-3 mr-2 text-gray-400"}),t.label]})},t.key)})}),f.length>50&&(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:_("found_tags",{count:f.length})})]}):(0,l.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:_(m?"no_tags_found":"start_typing")})})})]})}),(p||m)&&(0,l.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{x(!1),g("")}}),e.length>=a&&(0,l.jsx)("p",{className:"text-sm text-amber-600",children:_("max_tags_limit",{max:a})})]})}},62688:(e,t,a)=>{a.d(t,{A:()=>u});var l=a(43210);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),i=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim(),c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,l.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:a=2,absoluteStrokeWidth:r,className:s="",children:i,iconNode:d,...u},m)=>(0,l.createElement)("svg",{ref:m,...o,width:t,height:t,stroke:e,strokeWidth:r?24*Number(a)/Number(t):a,className:n("lucide",s),...!i&&!c(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,l.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let a=(0,l.forwardRef)(({className:a,...s},c)=>(0,l.createElement)(d,{ref:c,iconNode:t,className:n(`lucide-${r(i(e))}`,`lucide-${e}`,a),...s}));return a.displayName=i(e),a}},83938:(e,t,a)=>{a.d(t,{Az:()=>l,z:()=>r});let l=["ai_assistant","chatgpt","conversational_ai","smart_qa","language_model","writing_assistant","content_generation","copywriting","blog_writing","marketing_copy","image_generation","image_editing","ai_painting","avatar_generation","background_removal","video_generation","video_editing","video_clipping","short_video_creation","video_subtitles","speech_synthesis","speech_recognition","music_generation","speech_to_text","text_to_speech","code_generation","code_completion","code_review","development_assistant","low_code_platform","data_analysis","data_visualization","business_intelligence","machine_learning","deep_learning","office_automation","document_processing","project_management","team_collaboration","note_taking","ui_design","logo_design","web_design","graphic_design","prototype_design","seo_optimization","social_media_marketing","email_marketing","content_marketing","market_analysis","machine_translation","real_time_translation","document_translation","voice_translation"],r=3},94865:(e,t,a)=>{a.d(t,{$g:()=>d,Ef:()=>c,Y$:()=>n,kX:()=>l,mV:()=>o,tF:()=>u,v4:()=>i,vS:()=>r});let l={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},r=[{id:"free",title:"免费发布",description:l.FREE_LAUNCH.description,price:l.FREE_LAUNCH.displayPrice,features:l.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:l.PRIORITY_LAUNCH.description,price:l.PRIORITY_LAUNCH.displayPrice,features:l.PRIORITY_LAUNCH.features,recommended:!0}],s={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"所有价格"},{value:s.FREE.value,label:s.FREE.label},{value:s.FREEMIUM.value,label:s.FREEMIUM.label},{value:s.PAID.value,label:s.PAID.label}],n=[{value:s.FREE.value,label:s.FREE.label},{value:s.FREEMIUM.value,label:s.FREEMIUM.label},{value:s.PAID.value,label:s.PAID.label}],c=e=>{switch(e){case s.FREE.value:return s.FREE.color;case s.FREEMIUM.value:return s.FREEMIUM.color;case s.PAID.value:return s.PAID.color;default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case s.FREE.value:return s.FREE.label;case s.FREEMIUM.value:return s.FREEMIUM.label;case s.PAID.value:return s.PAID.label;default:return e}},d=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,u=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)},99270:(e,t,a)=>{a.d(t,{A:()=>l});let l=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};