(()=>{var e={};e.id=8439,e.ids=[8439],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3845:(e,t,r)=>{var i={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function s(e){if(!r.o(i,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=i[e],s=t[0];return r.e(t[1]).then(()=>r.t(s,19))}s.keys=()=>Object.keys(i),s.id=3845,e.exports=s},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12688:(e,t,r)=>{"use strict";r.d(t,{IB:()=>i,q:()=>s});let i=["en","zh"],s="en"},17941:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(35471),s=r(12688);let a=(0,i.A)(async({locale:e})=>(s.IB.includes(e)||(e=s.q),{locale:e,messages:(await r(3845)(`./${e}.json`)).default}))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(56037),s=r.n(i),a=r(60366);let o=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:a.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({status:1,isActive:1}),o.index({category:1,status:1}),o.index({tags:1,status:1}),o.index({submittedBy:1}),o.index({launchDate:-1}),o.index({views:-1}),o.index({likes:-1}),o.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let n=s().models.Tool||s().model("Tool",o)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45196:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var i=r(78521),s=r(60687);function a({locale:e,...t}){if(!e)throw Error(void 0);return(0,s.jsx)(i.Dk,{locale:e,...t})}},46930:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},56037:e=>{"use strict";e.exports=require("mongoose")},56132:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>_,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{GET:()=>u});var s=r(96559),a=r(48088),o=r(37719),n=r(32190),d=r(75745),l=r(30762),c=r(80972);async function u(e){try{await (0,d.A)();let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"12"),s=t.get("status"),a=t.get("search"),o=t.get("sort")||"submittedAt",c=t.get("order")||"desc",u={};s&&"all"!==s&&("pending"===s?u.$and=[{status:"pending"},{$or:[{launchOption:"free"},{launchOption:"paid",paymentStatus:"completed"}]}]:u.status=s),a&&(u.$or=[{name:{$regex:a,$options:"i"}},{description:{$regex:a,$options:"i"}},{submittedBy:{$regex:a,$options:"i"}},{tags:{$in:[RegExp(a,"i")]}}]);let p=(r-1)*i,m={};m[o]="desc"===c?-1:1;let f=await l.A.find(u).sort(m).skip(p).limit(i).lean(),_=await l.A.countDocuments(u),g=Math.ceil(_/i),y=await l.A.aggregate([{$group:{_id:"$status",count:{$sum:1}}}]),h={total:_,draft:y.find(e=>"draft"===e._id)?.count||0,pending:y.find(e=>"pending"===e._id)?.count||0,approved:y.find(e=>"approved"===e._id)?.count||0,rejected:y.find(e=>"rejected"===e._id)?.count||0};return n.NextResponse.json({success:!0,data:{tools:f,stats:h,pagination:{currentPage:r,totalPages:g,totalItems:_,itemsPerPage:i,hasNextPage:r<g,hasPrevPage:r>1}}})}catch(r){console.error("Error fetching admin tools:",r);let t=(0,c.y3)(e);return n.NextResponse.json({success:!1,error:(0,c.Q$)(t,"errors.internal_error")},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/tools/route",pathname:"/api/admin/tools",filename:"route",bundlePath:"app/api/admin/tools/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:_}=p;function g(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>o,PZ:()=>n,RI:()=>l,ut:()=>d});var i=r(64348);let s=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function a(e){let t=await (0,i.A)({locale:e,namespace:"categories"});return s.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function o(e){return(await a(e)).map(e=>({value:e.slug,label:e.name}))}async function n(e,t){return(await a(t)).find(t=>t.slug===e)}let d=s.map(e=>e.slug),l=s.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64348:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(61120),s=r(92440),a=r(84604),o=(0,i.cache)(function(e,t){return function({_cache:e=(0,a.d)(),_formatters:t=(0,a.b)(e),getMessageFallback:r=a.f,messages:i,namespace:s,onError:o=a.g,...n}){return function({messages:e,namespace:t,...r},i){return e=e["!"],t=(0,a.r)(t,"!"),(0,a.e)({...r,messages:e,namespace:t})}({...n,onError:o,cache:e,formatters:t,getMessageFallback:r,messages:{"!":i},namespace:s?`!.${s}`:"!"},"!")}({...e,namespace:t})}),n=(0,i.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),o(await (0,s.A)(r),t)})},70490:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(56037),s=r.n(i);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let n=async function(){if(o.conn)return o.conn;o.promise||(o.promise=s().connect(a,{bufferCommands:!1}).then(e=>e));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},80972:(e,t,r)=>{"use strict";r.d(t,{Q$:()=>a,y3:()=>o});let i={errors:{fetch_failed:"获取数据失败",network_error:"网络错误，请重试",validation_failed:"验证失败",unauthorized:"未授权访问",forbidden:"禁止访问",not_found:"资源未找到",internal_error:"服务器内部错误",invalid_request:"无效请求",missing_required_field:"缺少必需字段",duplicate_name:"名称已存在",create_failed:"创建失败",update_failed:"更新失败",delete_failed:"删除失败"},success:{created:"创建成功",updated:"更新成功",deleted:"删除成功",submitted:"提交成功",approved:"批准成功",rejected:"拒绝成功",published:"发布成功"},tools:{fetch_failed:"获取工具列表失败",create_failed:"创建工具失败",name_required:"name 是必需的",description_required:"description 是必需的",website_required:"website 是必需的",category_required:"category 是必需的",pricing_required:"pricing 是必需的",submitter_name_required:"submitterName 是必需的",submitter_email_required:"submitterEmail 是必需的",name_exists:"该工具名称已存在",submit_success:"工具提交成功，等待审核",approve_success:"工具审核通过",reject_success:"工具已拒绝",approve_failed:"审核通过失败",reject_failed:"拒绝失败",not_found:"工具未找到",update_success:"工具更新成功",update_failed:"工具更新失败",launch_date_already_set:"此工具已经选择了发布日期",free_date_restriction:"免费选项只能选择一个月后的日期",paid_date_restriction:"付费选项最早只能选择明天的日期",launch_date_set_success:"发布日期设置成功，工具已进入审核队列",edit_not_allowed:"当前状态不允许修改发布日期",already_published:"工具已发布，无法修改发布日期",launch_date_updated:"发布日期修改成功",publish_success:"工具发布成功",publish_failed:"工具发布失败"},user:{not_found:"用户未找到",unauthorized:"用户未授权",profile_update_success:"个人资料更新成功",profile_update_failed:"个人资料更新失败"},auth:{invalid_credentials:"无效的登录凭据",code_sent:"验证码已发送",code_send_failed:"验证码发送失败",invalid_code:"无效的验证码",login_success:"登录成功",login_failed:"登录失败",logout_success:"退出成功"},payment:{create_intent_failed:"创建支付意图失败",payment_success:"支付成功",payment_failed:"支付失败",webhook_error:"Webhook 处理错误",order_created:"订单创建成功，请完成支付",upgrade_order_created:"升级订单创建成功，请完成支付"},upload:{no_file:"请选择要上传的文件",invalid_type:"只支持 JPEG、PNG、GIF、WebP 格式的图片",file_too_large:"文件大小不能超过 5MB",upload_failed:"文件上传失败",upload_success:"文件上传成功"}},s={errors:{fetch_failed:"Failed to fetch data",network_error:"Network error, please try again",validation_failed:"Validation failed",unauthorized:"Unauthorized access",forbidden:"Access forbidden",not_found:"Resource not found",internal_error:"Internal server error",invalid_request:"Invalid request",missing_required_field:"Missing required field",duplicate_name:"Name already exists",create_failed:"Creation failed",update_failed:"Update failed",delete_failed:"Deletion failed"},success:{created:"Created successfully",updated:"Updated successfully",deleted:"Deleted successfully",submitted:"Submitted successfully",approved:"Approved successfully",rejected:"Rejected successfully",published:"Published successfully"},tools:{fetch_failed:"Failed to fetch tools list",create_failed:"Failed to create tool",name_required:"name is required",description_required:"description is required",website_required:"website is required",category_required:"category is required",pricing_required:"pricing is required",submitter_name_required:"submitterName is required",submitter_email_required:"submitterEmail is required",name_exists:"Tool name already exists",submit_success:"Tool submitted successfully, awaiting review",approve_success:"Tool approved successfully",reject_success:"Tool rejected successfully",approve_failed:"Failed to approve tool",reject_failed:"Failed to reject tool",not_found:"Tool not found",update_success:"Tool updated successfully",update_failed:"Failed to update tool",launch_date_already_set:"This tool has already selected a launch date",free_date_restriction:"Free option can only select dates one month later",paid_date_restriction:"Paid option can only select dates from tomorrow",launch_date_set_success:"Launch date set successfully, tool entered review queue",edit_not_allowed:"Current status does not allow modifying launch date",already_published:"Tool already published, cannot modify launch date",launch_date_updated:"Launch date updated successfully",publish_success:"Tool published successfully",publish_failed:"Failed to publish tool"},user:{not_found:"User not found",unauthorized:"User unauthorized",profile_update_success:"Profile updated successfully",profile_update_failed:"Failed to update profile"},auth:{invalid_credentials:"Invalid credentials",code_sent:"Verification code sent",code_send_failed:"Failed to send verification code",invalid_code:"Invalid verification code",login_success:"Login successful",login_failed:"Login failed",logout_success:"Logout successful"},payment:{create_intent_failed:"Failed to create payment intent",payment_success:"Payment successful",payment_failed:"Payment failed",webhook_error:"Webhook processing error",order_created:"Order created successfully, please complete payment",upgrade_order_created:"Upgrade order created successfully, please complete payment"},upload:{no_file:"Please select a file to upload",invalid_type:"Only JPEG, PNG, GIF, WebP image formats are supported",file_too_large:"File size cannot exceed 5MB",upload_failed:"File upload failed",upload_success:"File uploaded successfully"}};function a(e,t){let r=t.split("."),a="zh"===e?i:s;for(let t of r)if(!a||"object"!=typeof a||!(t in a))return"zh"===e?"操作失败":"Operation failed";else a=a[t];return"string"==typeof a?a:"zh"===e?"操作失败":"Operation failed"}function o(e){let t=e.headers.get("x-locale");if("en"===t||"zh"===t)return t;let r=e.headers.get("accept-language")||"",i=new URL(e.url).pathname;return i.startsWith("/en/")?"en":i.startsWith("/zh/")?"zh":r.includes("en")?"en":"zh"}},80994:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,4999,9658,580],()=>r(56132));module.exports=i})();