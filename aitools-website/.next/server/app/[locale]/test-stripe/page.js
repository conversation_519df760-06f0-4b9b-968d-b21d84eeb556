(()=>{var e={};e.id=4228,e.ids=[4228],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18948:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51604:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),l=r(88170),i=r.n(l),o=r(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let c={children:["",{children:["[locale]",{children:["test-stripe",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,18948)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/test-stripe/page",pathname:"/[locale]/test-stripe",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},59802:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),a=r(43210),l=r(39010),i=r(46299),o=r(94865);let n=(0,l.c)("pk_test_51K8G6vHZxvPjnxC8Jw3r7UbsBk8bdoy8txs1BJBaL8bPUM04LapfMJZAoK30RMqjHIuF1ANtm3nIjx5QGdWWym3J00v6hTFdQM");function c(){let e=(0,i.t2)(),t=(0,i.HH)(),[r,l]=(0,a.useState)(!1),[n,c]=(0,a.useState)(""),d=async r=>{if(r.preventDefault(),!e||!t)return;l(!0);let{error:s}=await e.confirmPayment({elements:t,confirmParams:{return_url:`${window.location.origin}/test-stripe`},redirect:"if_required"});s?c(s.message||"支付失败"):c("支付成功！"),l(!1)};return(0,s.jsxs)("form",{onSubmit:d,className:"space-y-4",children:[(0,s.jsx)(i.He,{}),(0,s.jsx)("button",{disabled:!e||!t||r,className:"w-full bg-blue-600 text-white py-2 px-4 rounded disabled:opacity-50",children:r?"处理中...":`支付 ${(0,o.$g)(o.kX.PRIORITY_LAUNCH.displayPrice)}`}),n&&(0,s.jsx)("div",{className:"text-center text-sm",children:n})]})}function d(){let[e,t]=(0,a.useState)(""),[r,l]=(0,a.useState)(!1),d=async()=>{l(!0);try{let e=await fetch("/api/test/create-payment-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:o.kX.PRIORITY_LAUNCH.stripeAmount,currency:"cny"})}),r=await e.json();r.success?t(r.clientSecret):alert("创建支付失败: "+r.message)}catch(e){alert("创建支付失败")}finally{l(!1)}};return(0,s.jsxs)("div",{className:"max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Stripe 支付测试"}),e?(0,s.jsx)(i.S8,{stripe:n,options:{clientSecret:e,appearance:{theme:"stripe"}},children:(0,s.jsx)(c,{})}):(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("button",{onClick:d,disabled:r,className:"bg-green-600 text-white py-2 px-4 rounded disabled:opacity-50",children:r?"创建中...":"创建测试支付"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-2",children:["点击创建一个 ",(0,o.$g)(o.kX.PRIORITY_LAUNCH.displayPrice)," 的测试支付"]})]}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-gray-100 rounded text-sm",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"测试卡号:"}),(0,s.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,s.jsx)("li",{children:"成功: 4242 4242 4242 4242"}),(0,s.jsx)("li",{children:"失败: 4000 0000 0000 0002"}),(0,s.jsx)("li",{children:"需要验证: 4000 0025 0000 3155"})]}),(0,s.jsx)("p",{className:"mt-2 text-xs",children:"使用任意未来日期作为过期时间，任意3位数作为CVC"})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71331:(e,t,r)=>{Promise.resolve().then(r.bind(r,59802))},79551:e=>{"use strict";e.exports=require("url")},88363:(e,t,r)=>{Promise.resolve().then(r.bind(r,18948))},94865:(e,t,r)=>{"use strict";r.d(t,{$g:()=>d,Ef:()=>n,Y$:()=>o,kX:()=>s,mV:()=>c,tF:()=>u,v4:()=>i,vS:()=>a});let s={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:s.FREE_LAUNCH.description,price:s.FREE_LAUNCH.displayPrice,features:s.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:s.PRIORITY_LAUNCH.description,price:s.PRIORITY_LAUNCH.displayPrice,features:s.PRIORITY_LAUNCH.features,recommended:!0}],l={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"所有价格"},{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],o=[{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],n=e=>{switch(e){case l.FREE.value:return l.FREE.color;case l.FREEMIUM.value:return l.FREEMIUM.color;case l.PAID.value:return l.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case l.FREE.value:return l.FREE.label;case l.FREEMIUM.value:return l.FREEMIUM.label;case l.PAID.value:return l.PAID.label;default:return e}},d=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,u=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,6435,6699,8232,7580,2585],()=>r(51604));module.exports=s})();