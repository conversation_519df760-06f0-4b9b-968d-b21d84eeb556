(()=>{var e={};e.id=6577,e.ids=[6577],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21039:(e,t,s)=>{"use strict";s.d(t,{default:()=>f});var r=s(60687),a=s(43210),o=s(12340),n=s(39010),l=s(46299),i=s(77618);let c=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var d=s(85778),u=s(94865);function m({onSuccess:e,amount:t}){let s=(0,l.t2)(),n=(0,l.HH)(),[m,x]=(0,a.useState)(!1),[p,h]=(0,a.useState)(""),y=(0,o.a8)(),b=(0,i.c3)("payment"),f=y?.startsWith("/en")?"en":"zh",g=async t=>{if(t.preventDefault(),s&&n){x(!0),h("");try{let{error:t}=await s.confirmPayment({elements:n,confirmParams:{return_url:`${window.location.origin}/${f}/submit/success`},redirect:"if_required"});t?"card_error"===t.type||"validation_error"===t.type?h(t.message||b("payment_failed")):h(b("payment_error")):e()}catch(e){h(b("payment_processing_failed"))}finally{x(!1)}}};return(0,r.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:b("payment_method")}),(0,r.jsx)(l.He,{options:{layout:"tabs",defaultValues:{billingDetails:{address:{country:"CN"}}}}})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:b("billing_address")}),(0,r.jsx)(l.H1,{options:{mode:"billing",defaultValues:{address:{country:"CN"}}}})]}),p&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-red-600 text-sm",children:p})}),(0,r.jsx)("button",{type:"submit",disabled:!s||!n||m,className:"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors",children:m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c,{className:"h-5 w-5 mr-2 animate-spin"}),b("processing_payment")]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"h-5 w-5 mr-2"}),b("pay_now",{amount:(0,u.tF)(t)})]})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{className:"text-gray-500 text-xs",children:b("security_notice")})})]})}var x=s(93613),p=s(5336),h=s(99891),y=s(56976);let b=(0,n.c)("pk_test_51K8G6vHZxvPjnxC8Jw3r7UbsBk8bdoy8txs1BJBaL8bPUM04LapfMJZAoK30RMqjHIuF1ANtm3nIjx5QGdWWym3J00v6hTFdQM");function f({order:e,orderId:t}){let s=(0,o.rd)(),[n,c]=(0,a.useState)(""),[f,g]=(0,a.useState)(""),[j,_]=(0,a.useState)(!0),v=(0,i.c3)("checkout"),N=async()=>{try{let e=await y.u.processOrderPayment(t,{paymentMethod:"stripe"});e.success?console.log("订单状态更新成功:",e.data):console.warn("订单状态更新失败:",e.error)}catch(e){console.error("调用订单支付接口失败:",e)}s.push(`/submit/success?toolId=${e.toolId}&paid=true`)};return j?(0,r.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:v("creating_payment_session")})]})}):f?(0,r.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:v("payment_error_title")}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:f}),(0,r.jsx)("button",{onClick:()=>s.push("/submit"),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:v("back_to_submit")})]})}):(0,r.jsxs)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)(d.A,{className:"h-12 w-12 text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:v("page_title")}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:v("page_subtitle")})]}),(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:v("order_details")}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:v("service_type")}),(0,r.jsx)("span",{className:"font-medium",children:v("tool_priority_launch")})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:v("tool_name")}),(0,r.jsx)("span",{className:"font-medium",children:e.tool?.name||v("loading_placeholder")})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:v("launch_date")}),(0,r.jsx)("span",{className:"font-medium",children:new Date(e.selectedLaunchDate).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:v("order_number")}),(0,r.jsx)("span",{className:"font-medium text-sm",children:e._id})]}),(0,r.jsx)("hr",{className:"my-4"}),(0,r.jsxs)("div",{className:"flex justify-between text-lg font-semibold",children:[(0,r.jsx)("span",{children:v("total")}),(0,r.jsx)("span",{className:"text-blue-600",children:(0,u.tF)(e.amount)})]})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-3",children:v("priority_service_title")}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),v("feature_any_date")]}),(0,r.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),v("feature_priority_review")]}),(0,r.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),v("feature_homepage_featured")]}),(0,r.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),v("feature_dedicated_support")]})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 mr-2 text-green-500"}),(0,r.jsx)("span",{className:"text-sm",children:v("security_notice")})]})}),n&&(0,r.jsx)(l.S8,{stripe:b,options:{clientSecret:n,appearance:{theme:"stripe",variables:{colorPrimary:"#2563eb"}}},children:(0,r.jsx)(m,{onSuccess:N,amount:e.amount})}),(0,r.jsx)("p",{className:"text-gray-500 text-sm mt-4 text-center",children:v("terms_notice")})]})}},28354:e=>{"use strict";e.exports=require("util")},28819:(e,t,s)=>{Promise.resolve().then(s.bind(s,84731))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},56976:(e,t,s)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function o(){return"production"}function n(){return"development"===o()}s.d(t,{u:()=>d});let l={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:o(),isDevelopment:n(),isProduction:"production"===o(),port:process.env.PORT||"3001"};n()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",l.baseUrl),console.log("  API Base URL:",l.apiBaseUrl),console.log("  NextAuth URL:",l.nextAuthUrl),console.log("  Environment:",l.environment),console.log("  Port:",l.port));let i=a();class c{constructor(e=i){this.baseURL=e}async request(e,t={}){try{let s=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(s,r),o=await a.json();if(!a.ok)throw Error(o.error||`HTTP error! status: ${a.status}`);return o}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/tools${s?`?${s}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/user/liked-tools${s?`?${s}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/admin/tools${s?`?${s}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,t){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(t)})}}let d=new c},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63126:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),o=s(88170),n=s.n(o),l=s(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(t,i);let c={children:["",{children:["[locale]",{children:["payment",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,92413)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/payment/checkout/page",pathname:"/[locale]/payment/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},65771:(e,t,s)=>{Promise.resolve().then(s.bind(s,21039))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84731:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/CheckoutClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/CheckoutClient.tsx","default")},92413:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(37413),a=s(39916),o=s(35426),n=s(12909),l=s(84731);let i=(0,s(26373).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var c=s(75745),d=s(31098),u=s(17063),m=s(56037),x=s.n(m),p=s(64348);async function h(e,t){try{if(await (0,c.A)(),!x().Types.ObjectId.isValid(e))return null;let s=await u.A.findOne({email:t});if(!s)return null;let r=await d.A.findById(e).populate("toolId","name description");if(!r||r.userId.toString()!==s._id.toString())return null;return{_id:r._id.toString(),type:r.type,amount:r.amount,currency:r.currency,status:r.status,description:r.description,selectedLaunchDate:r.selectedLaunchDate?r.selectedLaunchDate.toISOString():null,createdAt:r.createdAt?r.createdAt.toISOString():null,paidAt:r.paidAt?r.paidAt.toISOString():null,tool:r.toolId?{_id:r.toolId._id.toString(),name:r.toolId.name,description:r.toolId.description}:null,toolId:r.toolId?r.toolId._id.toString():null}}catch(e){return console.error("Failed to fetch order:",e),null}}async function y({searchParams:e,params:t}){let s=await (0,o.getServerSession)(n.N),{orderId:c}=await e,{locale:d}=await t,u=await (0,p.A)("checkout");if(s?.user?.email||(0,a.redirect)("/"),!c)return(0,r.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:u("order_not_found")}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:u("order_not_found_desc")})]})});let m=await h(c,s.user.email);return m?("completed"===m.status&&(0,a.redirect)(`/${d}/submit/success?toolId=${m.toolId}`),"pending"!==m.status)?(0,r.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:u("order_status_error")}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:u("order_status_error_desc")})]})}):(0,r.jsx)(l.default,{order:m,orderId:c}):(0,r.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:u("order_not_found")}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:u("order_deleted_desc")})]})})}},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},99891:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,9658,6435,6699,8232,3136,7580,2585,3930],()=>s(63126));module.exports=r})();