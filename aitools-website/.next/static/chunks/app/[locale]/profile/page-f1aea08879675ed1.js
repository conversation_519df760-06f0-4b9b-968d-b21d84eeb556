(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2943],{981:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(5695),a=s(2115),l=s.t(a,2),n=s(3385),o=l["use".trim()],i=s(3225),c=s(6160),d=s(469),m=s(5155),h=s(8986);function u(e){let{Link:t,config:s,getPathname:l,...u}=function(e,t){var s,l,n;let h={...s=t||{},localePrefix:"object"==typeof(n=s.localePrefix)?n:{mode:n||"always"},localeCookie:!!((l=s.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof l&&l},localeDetection:s.localeDetection??!0,alternateLinks:s.alternateLinks??!0},u=h.pathnames,x=(0,a.forwardRef)(function({href:t,locale:s,...r},a){let l,n;"object"==typeof t?(l=t.pathname,n=t.params):l=t;let d=(0,i._x)(t),x=e(),p=(0,i.yL)(x)?o(x):x,g=d?f({locale:s||p,href:null==u?l:{pathname:l,params:n},forcePrefix:null!=s||void 0}):l;return(0,m.jsx)(c.default,{ref:a,href:"object"==typeof t?{...t,pathname:g}:g,locale:s,localeCookie:h.localeCookie,...r})});function f(e){let t,{forcePrefix:s,href:r,locale:a}=e;return null==u?"object"==typeof r?(t=r.pathname,r.query&&(t+=(0,d.Zn)(r.query))):t=r:t=(0,d.FP)({locale:a,...(0,d.TK)(r),pathnames:h.pathnames}),(0,d.x3)(t,a,h,s)}function p(e){return function(t,...s){return e(f(t),...s)}}return{config:h,Link:x,redirect:p(r.redirect),permanentRedirect:p(r.permanentRedirect),getPathname:f}}(n.Ym,e);return{...u,Link:t,usePathname:function(){let e=function(e){let t=(0,r.usePathname)(),s=(0,n.Ym)();return(0,a.useMemo)(()=>{if(!t)return t;let r=t,a=(0,i.XP)(s,e.localePrefix);if((0,i.wO)(a,t))r=(0,i.MY)(t,a);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,i.bL)(s);(0,i.wO)(e,t)&&(r=(0,i.MY)(t,e))}return r},[e.localePrefix,s,t])}(s),t=(0,n.Ym)();return(0,a.useMemo)(()=>e&&s.pathnames?(0,d.aM)(t,e,s.pathnames):e,[t,e])},useRouter:function(){let e=(0,r.useRouter)(),t=(0,n.Ym)(),o=(0,r.usePathname)();return(0,a.useMemo)(()=>{function r(e){return function(r,a){let{locale:n,...i}=a||{},c=[l({href:r,locale:n||t})];Object.keys(i).length>0&&c.push(i),e(...c),(0,h.A)(s.localeCookie,o,t,n)}}return{...e,push:r(e.push),replace:r(e.replace),prefetch:r(e.prefetch)}},[t,o,e])},getPathname:l}}},1272:(e,t,s)=>{Promise.resolve().then(s.bind(s,9429))},1976:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2388:(e,t,s)=>{"use strict";s.d(t,{N_:()=>n,a8:()=>i,rd:()=>c});var r=s(9984),a=s(981);let l=(0,r.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:n,redirect:o,usePathname:i,useRouter:c}=(0,a.A)(l)},2657:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2713:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2731:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5155);function a(e){let{size:t="md",className:s=""}=e;return(0,r.jsx)("div",{className:"flex justify-center items-center ".concat(s),children:(0,r.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},2839:(e,t,s)=>{"use strict";s.d(t,{u:()=>d});var r=s(9509);function a(){if(r.env.NEXT_PUBLIC_APP_URL)return r.env.NEXT_PUBLIC_APP_URL;{let{protocol:e,hostname:t,port:s}=window.location;return"".concat(e,"//").concat(t).concat(s?":".concat(s):"")}}function l(){if(r.env.NEXT_PUBLIC_API_BASE_URL)return r.env.NEXT_PUBLIC_API_BASE_URL;let e=a();return"".concat(e,"/api")}function n(){return"production"}function o(){return"development"===n()}a(),l(),r.env.NEXTAUTH_URL?r.env.NEXTAUTH_URL:a(),n(),o(),n(),window.location.port||window.location.protocol,o();let i=l();class c{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(s,r),l=await a.json();if(!a.ok)throw Error(l.error||"HTTP error! status: ".concat(a.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/tools".concat(s?"?".concat(s):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/user/liked-tools".concat(s?"?".concat(s):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/admin/tools".concat(s?"?".concat(s):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request("/orders/".concat(e))}async processOrderPayment(e,t){return this.request("/orders/".concat(e,"/pay"),{method:"POST",body:JSON.stringify(t)})}constructor(e=i){this.baseURL=e}}let d=new c},4616:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},7652:(e,t,s)=>{"use strict";s.d(t,{c3:()=>l});var r=s(3385);function a(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let l=a(0,r.c3);a(0,r.kc)},9074:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9429:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(5155),a=s(2115),l=s(2108),n=s(2388),o=s(7652),i=s(2731),c=s(2839);let d=(0,s(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var m=s(4616),h=s(2713),u=s(2657),x=s(1976),f=s(9074);function p(){var e,t,s,p,g,y;let{data:v,status:N}=(0,l.useSession)(),j=(0,n.rd)(),b=(0,o.c3)("profile"),[w,k]=(0,a.useState)({submittedTools:0,approvedTools:0,totalViews:0,totalLikes:0,likedTools:0}),[_,A]=(0,a.useState)([]),[L,P]=(0,a.useState)(!0);(0,a.useEffect)(()=>{if("unauthenticated"===N)return void j.push("/");"authenticated"===N&&T()},[N,j]);let T=async()=>{try{P(!0);let e=await c.u.getAdminTools();if(e.success&&e.data){let t=e.data.tools,s=t.filter(e=>"approved"===e.status);k({submittedTools:t.length,approvedTools:s.length,totalViews:s.reduce((e,t)=>e+t.views,0),totalLikes:s.reduce((e,t)=>e+t.likes,0),likedTools:0}),A(t.slice(0,3))}}catch(e){console.error("Error fetching user data:",e)}finally{P(!1)}};return"loading"===N||L?(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(i.A,{size:"lg",className:"py-20"})}):v?(0,r.jsx)(a.Fragment,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:(null==(e=v.user)?void 0:e.image)?(0,r.jsx)("img",{src:v.user.image,alt:v.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-2xl font-medium text-gray-600",children:(null==(s=v.user)||null==(t=s.name)?void 0:t.charAt(0))||"U"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:null==(p=v.user)?void 0:p.name}),(0,r.jsx)("p",{className:"text-gray-600",children:null==(g=v.user)?void 0:g.email}),(null==(y=v.user)?void 0:y.role)==="admin"&&(0,r.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:b("admin_badge")}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[b("join_date"),": ",new Date().toLocaleDateString()]})]})]}),(0,r.jsxs)(n.N_,{href:"/settings",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(d,{className:"mr-2 h-4 w-4"}),b("edit_profile")]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(m.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:b("stats.submitted_tools")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.submittedTools})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(h.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:b("stats.approved_tools")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.approvedTools})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(u.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:b("stats.total_views")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.totalViews})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-red-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:b("stats.total_likes")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.totalLikes})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)(n.N_,{href:"/profile/submitted",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-8 w-8 text-blue-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:b("actions.my_submitted_tools")}),(0,r.jsx)("p",{className:"text-gray-600",children:b("actions.manage_submitted_tools")})]})]})}),(0,r.jsx)(n.N_,{href:"/profile/liked",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-red-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:b("actions.my_favorites")}),(0,r.jsx)("p",{className:"text-gray-600",children:b("actions.view_favorites")})]})]})}),(0,r.jsx)(n.N_,{href:"/submit",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-8 w-8 text-green-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:b("actions.submit_new_tool")}),(0,r.jsx)("p",{className:"text-gray-600",children:b("actions.share_ai_tools")})]})]})})]}),_.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:b("recent_tools.title")}),(0,r.jsx)(n.N_,{href:"/profile/submitted",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:b("recent_tools.view_all")})]}),(0,r.jsx)("div",{className:"space-y-4",children:_.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-gray-500",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-3 w-3 mr-1"}),new Date(e.submittedAt).toLocaleDateString("zh-CN")]}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("approved"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:"approved"===e.status?"已通过":"pending"===e.status?"审核中":"已拒绝"})]})]}),"approved"===e.status&&(0,r.jsx)(n.N_,{href:"/tools/".concat(e._id),className:"ml-4 text-blue-600 hover:text-blue-700",children:(0,r.jsx)(u.A,{className:"h-5 w-5"})})]},e._id))})]})]})}):null}},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:d="",children:m,iconNode:h,...u}=e;return(0,r.createElement)("svg",{ref:t,...c,width:a,height:a,stroke:s,strokeWidth:n?24*Number(l)/Number(a):l,className:o("lucide",d),...!m&&!i(u)&&{"aria-hidden":"true"},...u},[...h.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let s=(0,r.forwardRef)((s,l)=>{let{className:i,...c}=s;return(0,r.createElement)(d,{ref:l,iconNode:t,className:o("lucide-".concat(a(n(e))),"lucide-".concat(e),i),...c})});return s.displayName=n(e),s}},9984:(e,t,s)=>{"use strict";function r(e){return e}s.d(t,{A:()=>r})}},e=>{var t=t=>e(e.s=t);e.O(0,[3385,6160,2108,8441,1684,7358],()=>t(1272)),_N_E=e.O()}]);