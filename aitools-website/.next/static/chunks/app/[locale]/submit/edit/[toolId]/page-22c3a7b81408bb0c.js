(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3217],{2765:(e,s,t)=>{"use strict";t.d(s,{J2:()=>c,OD:()=>l,VP:()=>n});var r=t(7652);let a=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];function l(){return(function(){let e=(0,r.c3)("categories");return a.map(s=>({slug:s.slug,name:e("category_names.".concat(s.slug)),description:e("category_descriptions.".concat(s.slug)),icon:s.icon,color:s.color}))})().map(e=>({value:e.slug,label:e.name}))}function c(){let e=(0,r.c3)("categories"),s=l();return[{value:"",label:e("all_categories")},...s]}function n(e){return(0,r.c3)("categories")("category_names.".concat(e))||e}async function i(e){let s=await getTranslations({locale:e,namespace:"categories"});return a.map(e=>({slug:e.slug,name:s("category_names.".concat(e.slug)),description:s("category_descriptions.".concat(e.slug)),icon:e.icon,color:e.color}))}a.map(e=>e.slug),a.reduce((e,s)=>(e[s.slug]=s,e),{})},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8778:(e,s,t)=>{Promise.resolve().then(t.bind(t,9092))},9092:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var r=t(5155),a=t(2115),l=t(2388),c=t(2108),n=t(2731),i=t(6063),o=t(3467),d=t(2765),m=t(7550),g=t(9869),x=t(8146),u=t(3487);function b(e){var s,t;let{params:b}=e,{data:p,status:h}=(0,c.useSession)(),j=(0,l.rd)(),y=(0,d.OD)(),[f,N]=(0,a.useState)(""),[v,w]=(0,a.useState)(null),[k,C]=(0,a.useState)(!0),[S,_]=(0,a.useState)(!1),[A,F]=(0,a.useState)(!1),[E,D]=(0,a.useState)({}),[O,T]=(0,a.useState)("idle"),[B,P]=(0,a.useState)(""),[z,G]=(0,a.useState)(""),[I,J]=(0,a.useState)(!1),[L,H]=(0,a.useState)({name:"",tagline:"",description:"",website:"",logo:"",category:"",tags:[],pricing:""});(0,a.useEffect)(()=>{b.then(e=>N(e.toolId))},[b]),(0,a.useEffect)(()=>{if(!f)return;let e=async()=>{try{let e=await fetch("/api/tools/".concat(f)),s=await e.json();if(s.success){let e=s.data;w(e),H({name:e.name||"",tagline:e.tagline||"",description:e.description||"",website:e.website||"",logo:e.logo||"",category:e.category||"",tags:e.tags||[],pricing:e.pricing||""}),G(e.logo||"")}else T("error"),P(s.message||"获取工具信息失败")}catch(e){console.error("获取工具信息失败:",e),T("error"),P("网络错误，请重试")}finally{C(!1)}};p?e():"loading"!==h&&C(!1)},[f,p,h]),(0,a.useEffect)(()=>{var e;v&&(null==p||null==(e=p.user)||e.email)},[v,p]);let M=o.Y$,U=()=>{let e={};return L.name.trim()||(e.name="工具名称是必填项"),L.description.trim()||(e.description="工具描述是必填项"),L.website.trim()||(e.website="官方网站是必填项"),L.category||(e.category="请选择一个分类"),L.pricing||(e.pricing="请选择价格模式"),L.website&&!L.website.match(/^https?:\/\/.+/)&&(e.website="请输入有效的网站地址（以 http:// 或 https:// 开头）"),D(e),0===Object.keys(e).length},V=async e=>{if(e.preventDefault(),!p)return void F(!0);if(U()){_(!0),T("idle");try{let e=await fetch("/api/tools/".concat(f),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:L.name,tagline:L.tagline,description:L.description,website:L.website,logo:z||void 0,category:L.category,tags:L.tags,pricing:L.pricing})}),s=await e.json();s.success?(T("success"),P("工具信息更新成功！"),setTimeout(()=>{j.push("/profile/submitted")},2e3)):(T("error"),P(s.error||"更新失败，请重试"))}catch(e){console.error("更新工具失败:",e),T("error"),P("网络错误，请重试")}finally{_(!1)}}},Y=async e=>{if(!e)return;J(!0);let s=new FormData;s.append("logo",e);try{let e=await fetch("/api/upload/logo",{method:"POST",body:s}),t=await e.json();t.success?(G(t.data.url),H(e=>({...e,logo:t.data.url}))):(T("error"),P(t.message||"上传失败"))}catch(e){console.error("上传失败:",e),T("error"),P("上传失败，请重试")}finally{J(!1)}};return"loading"===h||k?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(n.A,{size:"lg"})}):p?v?(0,r.jsxs)(a.Fragment,{children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)(l.N_,{href:"/profile/submitted",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"返回工具列表"]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"编辑工具信息"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"更新您的工具信息，让更多用户了解您的产品"})]}),"success"===O&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,r.jsx)("p",{className:"text-green-800",children:B})}),"error"===O&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsx)("p",{className:"text-red-800",children:B})}),v&&(0,r.jsxs)("div",{className:"mb-6 p-4 rounded-lg border",children:["draft"===v.status&&(0,r.jsx)("div",{className:"bg-gray-50 border-gray-200",children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,r.jsx)("span",{className:"font-medium",children:"草稿状态："}),"可以编辑所有信息"]})}),"pending"===v.status&&(0,r.jsx)("div",{className:"bg-yellow-50 border-yellow-200",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-700",children:[(0,r.jsx)("span",{className:"font-medium",children:"审核中："}),"可以编辑所有信息，但建议谨慎修改"]})}),"approved"===v.status&&(0,r.jsx)("div",{className:"bg-blue-50 border-blue-200",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,r.jsx)("span",{className:"font-medium",children:"已通过审核："}),"可以编辑基础信息，但不能修改网站地址、分类、价格模式和标签"]})}),"approved"===v.status&&v.launchDate&&new Date(v.launchDate)<=new Date&&(0,r.jsx)("div",{className:"bg-green-50 border-green-200",children:(0,r.jsxs)("p",{className:"text-sm text-green-700",children:[(0,r.jsx)("span",{className:"font-medium",children:"已发布："}),"只能编辑名称、简介、描述等基础信息"]})}),"rejected"===v.status&&(0,r.jsx)("div",{className:"bg-red-50 border-red-200",children:(0,r.jsxs)("p",{className:"text-sm text-red-700",children:[(0,r.jsx)("span",{className:"font-medium",children:"审核被拒："}),"可以编辑所有信息后重新提交"]})})]}),(0,r.jsxs)("form",{onSubmit:V,className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"基本信息"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具名称 *"}),(0,r.jsx)("input",{type:"text",value:L.name,onChange:e=>H(s=>({...s,name:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(E.name?"border-red-300":"border-gray-300"),placeholder:"例如：ChatGPT"}),E.name&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:E.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具标语"}),(0,r.jsx)("input",{type:"text",value:L.tagline,onChange:e=>H(s=>({...s,tagline:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：AI驱动的对话助手"})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具描述 *"}),(0,r.jsx)("textarea",{value:L.description,onChange:e=>H(s=>({...s,description:e.target.value})),rows:3,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(E.description?"border-red-300":"border-gray-300"),placeholder:"简要描述您的工具功能和特点..."}),E.description&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:E.description})]}),["draft","pending","rejected"].includes((null==v?void 0:v.status)||"")&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"官方网站 *"}),(0,r.jsx)("input",{type:"url",value:L.website,onChange:e=>H(s=>({...s,website:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(E.website?"border-red-300":"border-gray-300"),placeholder:"https://example.com"}),E.website&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:E.website})]}),["approved","published"].includes((null==v?void 0:v.status)||"")&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"官方网站"}),(0,r.jsx)("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:L.website}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:(null==v?void 0:v.status)==="approved"?"审核通过后不可修改网站地址":"已发布工具不可修改网站地址"})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"工具Logo"}),(0,r.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"上传Logo图片"}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];t&&Y(t)},className:"hidden",id:"logo-upload"}),(0,r.jsxs)("label",{htmlFor:"logo-upload",className:"cursor-pointer",children:[(0,r.jsx)(g.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:I?"上传中...":"点击上传或拖拽图片到此处"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"支持 PNG, JPG, GIF 格式，建议尺寸 200x200px"})]})]})]}),z&&(0,r.jsx)("div",{className:"w-24 h-24 border border-gray-300 rounded-lg overflow-hidden",children:(0,r.jsx)("img",{src:z,alt:"Logo预览",className:"w-full h-full object-cover"})})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"分类和定价"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[["draft","pending","rejected"].includes((null==v?void 0:v.status)||"")?(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具分类 *"}),(0,r.jsxs)("select",{value:L.category,onChange:e=>H(s=>({...s,category:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(E.category?"border-red-300":"border-gray-300"),children:[(0,r.jsx)("option",{value:"",children:"请选择分类"}),y.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),E.category&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:E.category})]}):(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具分类"}),(0,r.jsx)("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:(null==(s=y.find(e=>e.value===L.category))?void 0:s.label)||L.category}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"分类不可修改"})]}),["draft","pending","rejected"].includes((null==v?void 0:v.status)||"")?(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格模式 *"}),(0,r.jsxs)("select",{value:L.pricing,onChange:e=>H(s=>({...s,pricing:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(E.pricing?"border-red-300":"border-gray-300"),children:[(0,r.jsx)("option",{value:"",children:"请选择价格模式"}),M.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),E.pricing&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:E.pricing})]}):(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格模式"}),(0,r.jsx)("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:(null==(t=M.find(e=>e.value===L.pricing))?void 0:t.label)||L.pricing}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"价格模式不可修改"})]})]})]}),["draft","pending","rejected"].includes((null==v?void 0:v.status)||"")?(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(u.A,{selectedTags:L.tags,onTagsChange:e=>H(s=>({...s,tags:e})),maxTags:x.z})}):(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"标签"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"标签不可修改"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:L.tags.map((e,s)=>(0,r.jsx)("span",{className:"inline-flex items-center px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full",children:e},s))})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{type:"submit",disabled:S,className:"px-8 py-3 rounded-lg font-medium transition-colors ".concat(S?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:S?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{size:"sm",className:"mr-2"}),"更新中..."]}):"更新工具信息"})})]})]}),(0,r.jsx)(i.A,{isOpen:A,onClose:()=>F(!1)})]}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"工具不存在"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"您要编辑的工具不存在或已被删除"}),(0,r.jsx)(l.N_,{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"返回工具列表"})]})}):(0,r.jsxs)(a.Fragment,{children:[(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"请先登录"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"您需要登录后才能编辑工具信息"}),(0,r.jsx)("button",{onClick:()=>F(!0),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"登录"})]})}),(0,r.jsx)(i.A,{isOpen:A,onClose:()=>F(!1)})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,3385,6160,2108,1577,4633,8441,1684,7358],()=>s(8778)),_N_E=e.O()}]);