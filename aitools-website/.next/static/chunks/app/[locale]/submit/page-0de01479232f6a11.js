(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1298],{646:(e,s,l)=>{"use strict";l.d(s,{A:()=>r});let r=(0,l(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5339:(e,s,l)=>{"use strict";l.d(s,{A:()=>r});let r=(0,l(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5734:(e,s,l)=>{"use strict";l.d(s,{A:()=>o});var r=l(5155),a=l(646),t=l(4416);function o(e){let{message:s,onClose:l,className:o=""}=e;return(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(o),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:s})}),l&&(0,r.jsx)("button",{onClick:l,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,r.jsx)(t.A,{className:"w-4 h-4"})})]})})}},6096:(e,s,l)=>{"use strict";l.d(s,{default:()=>t});var r=l(3385),a=l(5155);function t(e){let{locale:s,...l}=e;if(!s)throw Error(void 0);return(0,a.jsx)(r.Dk,{locale:s,...l})}},6681:(e,s,l)=>{"use strict";l.d(s,{default:()=>j});var r=l(5155),a=l(2115),t=l(2108),o=l(2388),i=l(7652),n=l(2731),d=l(9783),c=l(5734),m=l(6063),u=l(3467),x=l(9946);let g=(0,x.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),h=(0,x.A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);var f=l(9869),b=l(8146),p=l(3487);function j(e){let{categoryOptions:s,tagOptions:l}=e,x=(0,i.c3)("submit"),{data:j}=(0,t.useSession)(),y=(0,o.rd)(),[N,v]=(0,a.useState)({name:"",tagline:"",description:"",website:"",logoFile:null,category:"",tags:[],pricing:""}),[w,k]=(0,a.useState)(null),[_,A]=(0,a.useState)(!1),[F,C]=(0,a.useState)("idle"),[S,q]=(0,a.useState)(!1),M=e=>{let{name:s,value:l}=e.target;v(e=>({...e,[s]:l}))},U=()=>{let e=[{key:"name",label:x("form.tool_name")},{key:"tagline",label:x("form.tagline")},{key:"description",label:x("form.description")},{key:"websiteUrl",label:x("form.website_url")},{key:"category",label:x("form.category")},{key:"pricingModel",label:x("form.pricing_model")}].filter(e=>!N[e.key]);return e.length>0?(alert(x("form.missing_required_fields")+":\n"+e.map(e=>"- ".concat(e.label)).join("\n")),!1):N.logoFile?0!==N.tags.length||(alert(x("form.tags_placeholder")),!1):(alert(x("form.logo_required")),!1)},T=async e=>{var s;if(e.preventDefault(),!(null==j||null==(s=j.user)?void 0:s.email))return void q(!0);if(U()){A(!0),C("idle");try{let e="";if(N.logoFile){let s=new FormData;s.append("logo",N.logoFile);let l=await fetch("/api/upload/logo",{method:"POST",body:s});if(l.ok)e=(await l.json()).data.url;else{let e=await l.json();throw Error(e.message||"Logo upload failed")}}let s={name:N.name,tagline:N.tagline,description:N.description,website:N.websiteUrl,logoUrl:e,category:N.category,tags:N.tags,pricing:N.pricingModel},l=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(l.ok){let e=await l.json();C("success"),setTimeout(()=>{y.push("/submit/success?toolId=".concat(e.toolId))},2e3)}else{let e=await l.json();throw Error(e.message||"Submission failed")}}catch(e){console.error("Submit error:",e),C("error")}finally{A(!1)}}};return"success"===F?(0,r.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,r.jsx)(c.A,{message:x("form.success_message")})}):(0,r.jsxs)(a.Fragment,{children:[(0,r.jsxs)("form",{onSubmit:T,className:"max-w-4xl mx-auto space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(g,{className:"h-5 w-5 mr-2 text-blue-600"}),x("form.basic_info")]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[x("form.tool_name")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",value:N.name,onChange:M,placeholder:x("form.tool_name_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"tagline",className:"block text-sm font-medium text-gray-700 mb-2",children:[x("form.tagline")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",id:"tagline",name:"tagline",value:N.tagline,onChange:M,placeholder:x("form.tagline_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[x("form.description")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("textarea",{id:"description",name:"description",value:N.description,onChange:M,placeholder:x("form.description_placeholder"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("label",{htmlFor:"websiteUrl",className:"block text-sm font-medium text-gray-700 mb-2",children:[x("form.website_url")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(h,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"url",id:"websiteUrl",name:"websiteUrl",value:N.websiteUrl,onChange:M,placeholder:x("form.website_url_placeholder"),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("label",{htmlFor:"logo",className:"block text-sm font-medium text-gray-700 mb-2",children:[x("form.logo_upload")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("input",{type:"file",id:"logo",name:"logo",accept:"image/*",onChange:e=>{var s;let l=null==(s=e.target.files)?void 0:s[0];if(l){v(e=>({...e,logoFile:l}));let e=new FileReader;e.onload=e=>{var s;k(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(l)}},className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:x("form.logo_upload_hint")})]}),w&&(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-16 h-16 border border-gray-300 rounded-md overflow-hidden",children:(0,r.jsx)("img",{src:w,alt:x("form.logo_preview"),className:"w-full h-full object-cover"})})})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:x("form.category_and_pricing")}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:[x("form.category")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"category",name:"category",value:N.category,onChange:M,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,r.jsx)("option",{value:"",children:x("form.category_placeholder")}),s.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"pricingModel",className:"block text-sm font-medium text-gray-700 mb-2",children:[x("form.pricing_model")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"pricingModel",name:"pricingModel",value:N.pricingModel,onChange:M,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,r.jsx)("option",{value:"",children:x("form.pricing_placeholder")}),u.Y$.map(e=>(0,r.jsx)("option",{value:e.value,children:x("form.".concat(e.value))},e.value))]})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[x("form.tags")," ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)(p.A,{selectedTags:N.tags,onTagsChange:e=>{v(s=>({...s,tags:e}))},maxTags:b.z,placeholder:x("form.tags_placeholder")})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:x("form.guidelines_title")}),(0,r.jsxs)("ul",{className:"space-y-2 text-blue-800",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),x("form.guideline_1")]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),x("form.guideline_2")]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),x("form.guideline_3")]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),x("form.guideline_4")]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),x("form.guideline_5")]})]})]}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("button",{type:"submit",disabled:_,className:"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:_?(0,r.jsxs)(a.Fragment,{children:[(0,r.jsx)(n.A,{size:"sm",className:"mr-2"}),x("form.submitting")]}):(0,r.jsxs)(a.Fragment,{children:[(0,r.jsx)(f.A,{className:"h-5 w-5 mr-2"}),x("form.submit_button")]})})}),"error"===F&&(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(d.A,{message:x("form.error_message")})})]}),(0,r.jsx)(m.A,{isOpen:S,onClose:()=>q(!1)})]})}},8493:(e,s,l)=>{Promise.resolve().then(l.bind(l,6096)),Promise.resolve().then(l.bind(l,6681))},9783:(e,s,l)=>{"use strict";l.d(s,{A:()=>o});var r=l(5155),a=l(5339),t=l(4416);function o(e){let{message:s,onClose:l,className:o=""}=e;return(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(o),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:s})}),l&&(0,r.jsx)("button",{onClick:l,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(t.A,{className:"w-4 h-4"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,3385,6160,2108,1577,4633,8441,1684,7358],()=>s(8493)),_N_E=e.O()}]);