[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/about/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/BackButton.tsx": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/contact/page.tsx": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/page.tsx": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/CheckoutClient.tsx": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/privacy/page.tsx": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/page.tsx": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/ActionButtons.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/EditLaunchDateButton.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/terms/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/page.tsx": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "54", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts": "55", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts": "56", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "57", "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx": "58", "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts": "59", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "60", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "61", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx": "62", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "63", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "64", "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx": "65", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "66", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "67", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "68", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "69", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx": "70", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx": "71", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx": "72", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Footer.tsx": "73", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "74", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx": "75", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx": "76", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx": "77", "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/LikedToolsClient.tsx": "78", "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/SubmittedToolsListClient.tsx": "79", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "80", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "81", "/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx": "82", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx": "83", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx": "84", "/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx": "85", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "86", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "87", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx": "88", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx": "89", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx": "90", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx": "91", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories-i18n.ts": "92", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts": "93", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts": "94", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags-i18n.ts": "95", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "96", "/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx": "97", "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts": "98", "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/request.ts": "99", "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/routing.ts": "100", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api-messages.ts": "101", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "102", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "103", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts": "104", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "105", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "106", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts": "107", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts": "108", "/Users/<USER>/workspace/aitools/aitools-website/src/middleware.ts": "109", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "110", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "111", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts": "112", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "113", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "114", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "115"}, {"size": 8982, "mtime": 1751080369147, "results": "116", "hashOfConfig": "117"}, {"size": 12973, "mtime": 1751216809007, "results": "118", "hashOfConfig": "117"}, {"size": 18534, "mtime": 1751284027255, "results": "119", "hashOfConfig": "117"}, {"size": 6022, "mtime": 1751216761592, "results": "120", "hashOfConfig": "117"}, {"size": 515, "mtime": 1751216207594, "results": "121", "hashOfConfig": "117"}, {"size": 11242, "mtime": 1751191337661, "results": "122", "hashOfConfig": "117"}, {"size": 8412, "mtime": 1751182074559, "results": "123", "hashOfConfig": "117"}, {"size": 7365, "mtime": 1751247899087, "results": "124", "hashOfConfig": "117"}, {"size": 11513, "mtime": 1751080448998, "results": "125", "hashOfConfig": "117"}, {"size": 13683, "mtime": 1751217289644, "results": "126", "hashOfConfig": "117"}, {"size": 5798, "mtime": 1751214976702, "results": "127", "hashOfConfig": "117"}, {"size": 15632, "mtime": 1751247200707, "results": "128", "hashOfConfig": "117"}, {"size": 7255, "mtime": 1751296966467, "results": "129", "hashOfConfig": "117"}, {"size": 3919, "mtime": 1751296679953, "results": "130", "hashOfConfig": "117"}, {"size": 10428, "mtime": 1751174621877, "results": "131", "hashOfConfig": "117"}, {"size": 2140, "mtime": 1751216235923, "results": "132", "hashOfConfig": "117"}, {"size": 10571, "mtime": 1751216222080, "results": "133", "hashOfConfig": "117"}, {"size": 2031, "mtime": 1751286952297, "results": "134", "hashOfConfig": "117"}, {"size": 2799, "mtime": 1751127821278, "results": "135", "hashOfConfig": "117"}, {"size": 21012, "mtime": 1751216524695, "results": "136", "hashOfConfig": "117"}, {"size": 22788, "mtime": 1751217260901, "results": "137", "hashOfConfig": "117"}, {"size": 2222, "mtime": 1751290700965, "results": "138", "hashOfConfig": "117"}, {"size": 6417, "mtime": 1751275156496, "results": "139", "hashOfConfig": "117"}, {"size": 1484, "mtime": 1751293935295, "results": "140", "hashOfConfig": "117"}, {"size": 801, "mtime": 1751267570019, "results": "141", "hashOfConfig": "117"}, {"size": 804, "mtime": 1751290149892, "results": "142", "hashOfConfig": "117"}, {"size": 10018, "mtime": 1751290078647, "results": "143", "hashOfConfig": "117"}, {"size": 11961, "mtime": 1751080591727, "results": "144", "hashOfConfig": "117"}, {"size": 4543, "mtime": 1750930937103, "results": "145", "hashOfConfig": "117"}, {"size": 6972, "mtime": 1751018762448, "results": "146", "hashOfConfig": "117"}, {"size": 3885, "mtime": 1751018851458, "results": "147", "hashOfConfig": "117"}, {"size": 5752, "mtime": 1751217219404, "results": "148", "hashOfConfig": "117"}, {"size": 5801, "mtime": 1751247859534, "results": "149", "hashOfConfig": "117"}, {"size": 5321, "mtime": 1750906802986, "results": "150", "hashOfConfig": "117"}, {"size": 2206, "mtime": 1751160497677, "results": "151", "hashOfConfig": "117"}, {"size": 2381, "mtime": 1751160563812, "results": "152", "hashOfConfig": "117"}, {"size": 3265, "mtime": 1751163869055, "results": "153", "hashOfConfig": "117"}, {"size": 171, "mtime": 1750921851894, "results": "154", "hashOfConfig": "117"}, {"size": 3714, "mtime": 1750921931408, "results": "155", "hashOfConfig": "117"}, {"size": 4683, "mtime": 1751160718688, "results": "156", "hashOfConfig": "117"}, {"size": 2289, "mtime": 1751181379570, "results": "157", "hashOfConfig": "117"}, {"size": 4267, "mtime": 1751161594662, "results": "158", "hashOfConfig": "117"}, {"size": 2553, "mtime": 1751161424205, "results": "159", "hashOfConfig": "117"}, {"size": 4357, "mtime": 1751288615702, "results": "160", "hashOfConfig": "117"}, {"size": 5242, "mtime": 1751013821668, "results": "161", "hashOfConfig": "117"}, {"size": 1022, "mtime": 1750984456438, "results": "162", "hashOfConfig": "117"}, {"size": 4812, "mtime": 1751162755345, "results": "163", "hashOfConfig": "117"}, {"size": 12978, "mtime": 1751291724412, "results": "164", "hashOfConfig": "117"}, {"size": 4227, "mtime": 1751162633491, "results": "165", "hashOfConfig": "117"}, {"size": 8858, "mtime": 1751199328528, "results": "166", "hashOfConfig": "117"}, {"size": 3803, "mtime": 1751161706933, "results": "167", "hashOfConfig": "117"}, {"size": 5368, "mtime": 1751160424453, "results": "168", "hashOfConfig": "117"}, {"size": 6117, "mtime": 1751296155444, "results": "169", "hashOfConfig": "117"}, {"size": 2794, "mtime": 1751164176243, "results": "170", "hashOfConfig": "117"}, {"size": 2491, "mtime": 1751160639981, "results": "171", "hashOfConfig": "117"}, {"size": 3444, "mtime": 1751161223047, "results": "172", "hashOfConfig": "117"}, {"size": 431, "mtime": 1751165597653, "results": "173", "hashOfConfig": "117"}, {"size": 245, "mtime": 1751244021142, "results": "174", "hashOfConfig": "117"}, {"size": 2921, "mtime": 1751181616429, "results": "175", "hashOfConfig": "117"}, {"size": 1552, "mtime": 1751216288085, "results": "176", "hashOfConfig": "117"}, {"size": 845, "mtime": 1750908285683, "results": "177", "hashOfConfig": "117"}, {"size": 9057, "mtime": 1751271932960, "results": "178", "hashOfConfig": "117"}, {"size": 77, "mtime": 1751171640781, "results": "179", "hashOfConfig": "117"}, {"size": 505, "mtime": 1750908273441, "results": "180", "hashOfConfig": "117"}, {"size": 3854, "mtime": 1751254369488, "results": "181", "hashOfConfig": "117"}, {"size": 863, "mtime": 1750908296528, "results": "182", "hashOfConfig": "117"}, {"size": 6526, "mtime": 1751292943182, "results": "183", "hashOfConfig": "117"}, {"size": 4698, "mtime": 1751248211337, "results": "184", "hashOfConfig": "117"}, {"size": 9251, "mtime": 1751216490067, "results": "185", "hashOfConfig": "117"}, {"size": 6887, "mtime": 1751243760925, "results": "186", "hashOfConfig": "117"}, {"size": 4645, "mtime": 1751243916016, "results": "187", "hashOfConfig": "117"}, {"size": 11332, "mtime": 1751243986103, "results": "188", "hashOfConfig": "117"}, {"size": 2993, "mtime": 1751243822873, "results": "189", "hashOfConfig": "117"}, {"size": 2451, "mtime": 1751243869428, "results": "190", "hashOfConfig": "117"}, {"size": 1680, "mtime": 1751216156055, "results": "191", "hashOfConfig": "117"}, {"size": 1554, "mtime": 1751217104534, "results": "192", "hashOfConfig": "117"}, {"size": 1240, "mtime": 1751243730311, "results": "193", "hashOfConfig": "117"}, {"size": 7475, "mtime": 1751247582750, "results": "194", "hashOfConfig": "117"}, {"size": 22424, "mtime": 1751291962464, "results": "195", "hashOfConfig": "117"}, {"size": 867, "mtime": 1750922283437, "results": "196", "hashOfConfig": "117"}, {"size": 362, "mtime": 1750922147686, "results": "197", "hashOfConfig": "117"}, {"size": 8948, "mtime": 1751216415293, "results": "198", "hashOfConfig": "117"}, {"size": 5275, "mtime": 1751023043127, "results": "199", "hashOfConfig": "117"}, {"size": 2605, "mtime": 1751019665417, "results": "200", "hashOfConfig": "117"}, {"size": 17144, "mtime": 1751296325358, "results": "201", "hashOfConfig": "117"}, {"size": 9319, "mtime": 1751216453727, "results": "202", "hashOfConfig": "117"}, {"size": 3366, "mtime": 1751216477033, "results": "203", "hashOfConfig": "117"}, {"size": 10116, "mtime": 1751217323841, "results": "204", "hashOfConfig": "117"}, {"size": 8869, "mtime": 1751216466888, "results": "205", "hashOfConfig": "117"}, {"size": 5433, "mtime": 1751023279983, "results": "206", "hashOfConfig": "117"}, {"size": 3048, "mtime": 1751023262678, "results": "207", "hashOfConfig": "117"}, {"size": 5155, "mtime": 1751181125061, "results": "208", "hashOfConfig": "117"}, {"size": 3849, "mtime": 1751072665492, "results": "209", "hashOfConfig": "117"}, {"size": 4478, "mtime": 1751271806862, "results": "210", "hashOfConfig": "117"}, {"size": 3036, "mtime": 1751293637039, "results": "211", "hashOfConfig": "117"}, {"size": 5288, "mtime": 1751292694381, "results": "212", "hashOfConfig": "117"}, {"size": 5354, "mtime": 1751086484747, "results": "213", "hashOfConfig": "117"}, {"size": 432, "mtime": 1751216104505, "results": "214", "hashOfConfig": "117"}, {"size": 410, "mtime": 1751211931429, "results": "215", "hashOfConfig": "117"}, {"size": 757, "mtime": 1751216119022, "results": "216", "hashOfConfig": "117"}, {"size": 10538, "mtime": 1751249910169, "results": "217", "hashOfConfig": "117"}, {"size": 7460, "mtime": 1751260407880, "results": "218", "hashOfConfig": "117"}, {"size": 5474, "mtime": 1751122879189, "results": "219", "hashOfConfig": "117"}, {"size": 3457, "mtime": 1751122835025, "results": "220", "hashOfConfig": "117"}, {"size": 921, "mtime": 1750903252798, "results": "221", "hashOfConfig": "117"}, {"size": 5018, "mtime": 1751186011040, "results": "222", "hashOfConfig": "117"}, {"size": 3274, "mtime": 1751081422931, "results": "223", "hashOfConfig": "117"}, {"size": 3985, "mtime": 1751017840303, "results": "224", "hashOfConfig": "117"}, {"size": 490, "mtime": 1751216137026, "results": "225", "hashOfConfig": "117"}, {"size": 1667, "mtime": 1750903308052, "results": "226", "hashOfConfig": "117"}, {"size": 2141, "mtime": 1750921803605, "results": "227", "hashOfConfig": "117"}, {"size": 3989, "mtime": 1750984256539, "results": "228", "hashOfConfig": "117"}, {"size": 4859, "mtime": 1751181597006, "results": "229", "hashOfConfig": "117"}, {"size": 3406, "mtime": 1751090710634, "results": "230", "hashOfConfig": "117"}, {"size": 720, "mtime": 1750903327281, "results": "231", "hashOfConfig": "117"}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ifueu7", {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 16, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 19, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/about/page.tsx", ["577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx", ["590", "591", "592", "593", "594", "595", "596", "597", "598", "599"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx", ["600", "601", "602", "603", "604", "605", "606", "607"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx", ["608", "609", "610"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/BackButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx", ["611", "612", "613", "614"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx", ["615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx", ["631", "632", "633", "634", "635", "636", "637", "638"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/contact/page.tsx", ["639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx", ["653"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/page.tsx", ["654"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/CheckoutClient.tsx", ["655", "656", "657"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/privacy/page.tsx", ["658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx", ["671"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/page.tsx", ["672", "673", "674"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx", ["675"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx", ["676", "677", "678", "679", "680", "681"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx", ["682"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx", ["683", "684", "685", "686"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx", ["687", "688", "689", "690", "691", "692"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/ActionButtons.tsx", ["693"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/EditLaunchDateButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/terms/page.tsx", ["694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx", ["713", "714", "715", "716"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx", ["717"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx", ["718"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/page.tsx", ["719", "720", "721", "722", "723", "724", "725", "726"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["727", "728"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["729", "730"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts", ["731"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts", ["732"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts", ["733", "734"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["735", "736", "737", "738", "739"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["740", "741", "742", "743"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["744"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts", ["745", "746", "747", "748"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx", ["749"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", ["750"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["751", "752", "753", "754", "755"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx", ["756", "757", "758"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx", ["759", "760"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx", ["761", "762"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx", ["763"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/LikedToolsClient.tsx", ["764"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/SubmittedToolsListClient.tsx", ["765", "766", "767", "768", "769"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx", ["770", "771", "772", "773", "774", "775", "776", "777", "778", "779"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx", ["780", "781", "782"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx", ["783", "784"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["785", "786", "787", "788"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", ["789", "790"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx", ["791", "792", "793", "794", "795"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx", ["796"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx", ["797"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx", ["798", "799", "800"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories-i18n.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags-i18n.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx", ["801"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/request.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/routing.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api-messages.ts", ["802"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["803", "804", "805", "806", "807"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["808", "809", "810", "811", "812"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts", ["813"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/middleware.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["814"], [], {"ruleId": "815", "severity": 2, "message": "816", "line": 89, "column": 15, "nodeType": "817", "endLine": 92, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 89, "column": 15, "nodeType": "817", "endLine": 92, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 89, "column": 15, "nodeType": "817", "endLine": 92, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 89, "column": 15, "nodeType": "817", "endLine": 92, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 89, "column": 15, "nodeType": "817", "endLine": 92, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 89, "column": 15, "nodeType": "817", "endLine": 92, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 89, "column": 15, "nodeType": "817", "endLine": 92, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "818", "line": 200, "column": 13, "nodeType": "817", "endLine": 203, "endColumn": 14}, {"ruleId": "815", "severity": 2, "message": "818", "line": 200, "column": 13, "nodeType": "817", "endLine": 203, "endColumn": 14}, {"ruleId": "815", "severity": 2, "message": "818", "line": 200, "column": 13, "nodeType": "817", "endLine": 203, "endColumn": 14}, {"ruleId": "815", "severity": 2, "message": "818", "line": 200, "column": 13, "nodeType": "817", "endLine": 203, "endColumn": 14}, {"ruleId": "815", "severity": 2, "message": "818", "line": 200, "column": 13, "nodeType": "817", "endLine": 203, "endColumn": 14}, {"ruleId": "815", "severity": 2, "message": "818", "line": 200, "column": 13, "nodeType": "817", "endLine": 203, "endColumn": 14}, {"ruleId": "819", "severity": 2, "message": "820", "line": 13, "column": 3, "nodeType": null, "messageId": "821", "endLine": 13, "endColumn": 8}, {"ruleId": "819", "severity": 2, "message": "822", "line": 18, "column": 3, "nodeType": null, "messageId": "821", "endLine": 18, "endColumn": 8}, {"ruleId": "819", "severity": 2, "message": "823", "line": 19, "column": 3, "nodeType": null, "messageId": "821", "endLine": 19, "endColumn": 11}, {"ruleId": "819", "severity": 2, "message": "824", "line": 22, "column": 3, "nodeType": null, "messageId": "821", "endLine": 22, "endColumn": 7}, {"ruleId": "819", "severity": 2, "message": "825", "line": 28, "column": 9, "nodeType": null, "messageId": "821", "endLine": 28, "endColumn": 17}, {"ruleId": "826", "severity": 1, "message": "827", "line": 40, "column": 6, "nodeType": "828", "endLine": 40, "endColumn": 17, "suggestions": "829"}, {"ruleId": "819", "severity": 2, "message": "830", "line": 54, "column": 14, "nodeType": null, "messageId": "821", "endLine": 54, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "831", "line": 61, "column": 9, "nodeType": null, "messageId": "821", "endLine": 61, "endColumn": 19}, {"ruleId": "819", "severity": 2, "message": "832", "line": 70, "column": 9, "nodeType": null, "messageId": "821", "endLine": 70, "endColumn": 24}, {"ruleId": "819", "severity": 2, "message": "833", "line": 83, "column": 9, "nodeType": null, "messageId": "821", "endLine": 83, "endColumn": 27}, {"ruleId": "819", "severity": 2, "message": "834", "line": 16, "column": 3, "nodeType": null, "messageId": "821", "endLine": 16, "endColumn": 6}, {"ruleId": "819", "severity": 2, "message": "825", "line": 27, "column": 9, "nodeType": null, "messageId": "821", "endLine": 27, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "835", "line": 31, "column": 9, "nodeType": null, "messageId": "821", "endLine": 31, "endColumn": 17}, {"ruleId": "826", "severity": 1, "message": "836", "line": 48, "column": 6, "nodeType": "828", "endLine": 48, "endColumn": 20, "suggestions": "837"}, {"ruleId": "819", "severity": 2, "message": "830", "line": 65, "column": 14, "nodeType": null, "messageId": "821", "endLine": 65, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "830", "line": 98, "column": 14, "nodeType": null, "messageId": "821", "endLine": 98, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "830", "line": 124, "column": 14, "nodeType": null, "messageId": "821", "endLine": 124, "endColumn": 17}, {"ruleId": "838", "severity": 1, "message": "839", "line": 288, "column": 27, "nodeType": "817", "endLine": 292, "endColumn": 29}, {"ruleId": "819", "severity": 2, "message": "840", "line": 22, "column": 3, "nodeType": null, "messageId": "821", "endLine": 22, "endColumn": 9}, {"ruleId": "819", "severity": 2, "message": "830", "line": 59, "column": 14, "nodeType": null, "messageId": "821", "endLine": 59, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "830", "line": 98, "column": 14, "nodeType": null, "messageId": "821", "endLine": 98, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "841", "line": 4, "column": 21, "nodeType": null, "messageId": "821", "endLine": 4, "endColumn": 25}, {"ruleId": "819", "severity": 2, "message": "842", "line": 9, "column": 3, "nodeType": null, "messageId": "821", "endLine": 9, "endColumn": 12}, {"ruleId": "838", "severity": 1, "message": "839", "line": 148, "column": 13, "nodeType": "817", "endLine": 152, "endColumn": 15}, {"ruleId": "838", "severity": 1, "message": "839", "line": 228, "column": 19, "nodeType": "817", "endLine": 233, "endColumn": 21}, {"ruleId": "819", "severity": 2, "message": "843", "line": 7, "column": 29, "nodeType": null, "messageId": "821", "endLine": 7, "endColumn": 44}, {"ruleId": "819", "severity": 2, "message": "844", "line": 9, "column": 10, "nodeType": null, "messageId": "821", "endLine": 9, "endColumn": 19}, {"ruleId": "819", "severity": 2, "message": "845", "line": 111, "column": 12, "nodeType": null, "messageId": "821", "endLine": 111, "endColumn": 17}, {"ruleId": "815", "severity": 2, "message": "816", "line": 228, "column": 15, "nodeType": "817", "endLine": 231, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 228, "column": 15, "nodeType": "817", "endLine": 231, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 228, "column": 15, "nodeType": "817", "endLine": 231, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 228, "column": 15, "nodeType": "817", "endLine": 231, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 228, "column": 15, "nodeType": "817", "endLine": 231, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 228, "column": 15, "nodeType": "817", "endLine": 231, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 228, "column": 15, "nodeType": "817", "endLine": 231, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "846", "line": 240, "column": 17, "nodeType": "817", "endLine": 243, "endColumn": 18}, {"ruleId": "815", "severity": 2, "message": "846", "line": 240, "column": 17, "nodeType": "817", "endLine": 243, "endColumn": 18}, {"ruleId": "815", "severity": 2, "message": "846", "line": 240, "column": 17, "nodeType": "817", "endLine": 243, "endColumn": 18}, {"ruleId": "815", "severity": 2, "message": "846", "line": 240, "column": 17, "nodeType": "817", "endLine": 243, "endColumn": 18}, {"ruleId": "815", "severity": 2, "message": "846", "line": 240, "column": 17, "nodeType": "817", "endLine": 243, "endColumn": 18}, {"ruleId": "815", "severity": 2, "message": "846", "line": 240, "column": 17, "nodeType": "817", "endLine": 243, "endColumn": 18}, {"ruleId": "819", "severity": 2, "message": "845", "line": 65, "column": 12, "nodeType": null, "messageId": "821", "endLine": 65, "endColumn": 17}, {"ruleId": "815", "severity": 2, "message": "816", "line": 184, "column": 15, "nodeType": "817", "endLine": 187, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 184, "column": 15, "nodeType": "817", "endLine": 187, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 184, "column": 15, "nodeType": "817", "endLine": 187, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 184, "column": 15, "nodeType": "817", "endLine": 187, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 184, "column": 15, "nodeType": "817", "endLine": 187, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 184, "column": 15, "nodeType": "817", "endLine": 187, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 184, "column": 15, "nodeType": "817", "endLine": 187, "endColumn": 16}, {"ruleId": "819", "severity": 2, "message": "847", "line": 5, "column": 38, "nodeType": null, "messageId": "821", "endLine": 5, "endColumn": 44}, {"ruleId": "815", "severity": 2, "message": "816", "line": 99, "column": 15, "nodeType": "817", "endLine": 102, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 99, "column": 15, "nodeType": "817", "endLine": 102, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 99, "column": 15, "nodeType": "817", "endLine": 102, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 99, "column": 15, "nodeType": "817", "endLine": 102, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 99, "column": 15, "nodeType": "817", "endLine": 102, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 99, "column": 15, "nodeType": "817", "endLine": 102, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 99, "column": 15, "nodeType": "817", "endLine": 102, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "848", "line": 174, "column": 19, "nodeType": "817", "endLine": 174, "endColumn": 80}, {"ruleId": "815", "severity": 2, "message": "848", "line": 174, "column": 19, "nodeType": "817", "endLine": 174, "endColumn": 80}, {"ruleId": "815", "severity": 2, "message": "848", "line": 174, "column": 19, "nodeType": "817", "endLine": 174, "endColumn": 80}, {"ruleId": "815", "severity": 2, "message": "848", "line": 174, "column": 19, "nodeType": "817", "endLine": 174, "endColumn": 80}, {"ruleId": "815", "severity": 2, "message": "848", "line": 174, "column": 19, "nodeType": "817", "endLine": 174, "endColumn": 80}, {"ruleId": "815", "severity": 2, "message": "848", "line": 174, "column": 19, "nodeType": "817", "endLine": 174, "endColumn": 80}, {"ruleId": "819", "severity": 2, "message": "830", "line": 95, "column": 14, "nodeType": null, "messageId": "821", "endLine": 95, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "845", "line": 81, "column": 12, "nodeType": null, "messageId": "821", "endLine": 81, "endColumn": 17}, {"ruleId": "849", "severity": 2, "message": "850", "line": 17, "column": 10, "nodeType": "851", "messageId": "852", "endLine": 17, "endColumn": 13, "suggestions": "853"}, {"ruleId": "826", "severity": 1, "message": "854", "line": 32, "column": 6, "nodeType": "828", "endLine": 32, "endColumn": 8, "suggestions": "855"}, {"ruleId": "819", "severity": 2, "message": "830", "line": 51, "column": 14, "nodeType": null, "messageId": "821", "endLine": 51, "endColumn": 17}, {"ruleId": "815", "severity": 2, "message": "816", "line": 65, "column": 15, "nodeType": "817", "endLine": 68, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 65, "column": 15, "nodeType": "817", "endLine": 68, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 65, "column": 15, "nodeType": "817", "endLine": 68, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 65, "column": 15, "nodeType": "817", "endLine": 68, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 65, "column": 15, "nodeType": "817", "endLine": 68, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 65, "column": 15, "nodeType": "817", "endLine": 68, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 65, "column": 15, "nodeType": "817", "endLine": 68, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "818", "line": 224, "column": 22, "nodeType": "817", "endLine": 224, "endColumn": 41}, {"ruleId": "815", "severity": 2, "message": "818", "line": 224, "column": 22, "nodeType": "817", "endLine": 224, "endColumn": 41}, {"ruleId": "815", "severity": 2, "message": "818", "line": 224, "column": 22, "nodeType": "817", "endLine": 224, "endColumn": 41}, {"ruleId": "815", "severity": 2, "message": "818", "line": 224, "column": 22, "nodeType": "817", "endLine": 224, "endColumn": 41}, {"ruleId": "815", "severity": 2, "message": "818", "line": 224, "column": 22, "nodeType": "817", "endLine": 224, "endColumn": 41}, {"ruleId": "815", "severity": 2, "message": "818", "line": 224, "column": 22, "nodeType": "817", "endLine": 224, "endColumn": 41}, {"ruleId": "849", "severity": 2, "message": "850", "line": 57, "column": 37, "nodeType": "851", "messageId": "852", "endLine": 57, "endColumn": 40, "suggestions": "856"}, {"ruleId": "819", "severity": 2, "message": "857", "line": 11, "column": 3, "nodeType": null, "messageId": "821", "endLine": 11, "endColumn": 7}, {"ruleId": "819", "severity": 2, "message": "858", "line": 18, "column": 3, "nodeType": null, "messageId": "821", "endLine": 18, "endColumn": 7}, {"ruleId": "838", "severity": 1, "message": "839", "line": 96, "column": 19, "nodeType": "817", "endLine": 100, "endColumn": 21}, {"ruleId": "849", "severity": 2, "message": "850", "line": 53, "column": 10, "nodeType": "851", "messageId": "852", "endLine": 53, "endColumn": 13, "suggestions": "859"}, {"ruleId": "819", "severity": 2, "message": "860", "line": 12, "column": 3, "nodeType": null, "messageId": "821", "endLine": 12, "endColumn": 7}, {"ruleId": "819", "severity": 2, "message": "834", "line": 15, "column": 3, "nodeType": null, "messageId": "821", "endLine": 15, "endColumn": 6}, {"ruleId": "819", "severity": 2, "message": "830", "line": 94, "column": 14, "nodeType": null, "messageId": "821", "endLine": 94, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "830", "line": 111, "column": 14, "nodeType": null, "messageId": "821", "endLine": 111, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "830", "line": 128, "column": 14, "nodeType": null, "messageId": "821", "endLine": 128, "endColumn": 17}, {"ruleId": "838", "severity": 1, "message": "839", "line": 197, "column": 23, "nodeType": "817", "endLine": 201, "endColumn": 25}, {"ruleId": "838", "severity": 1, "message": "839", "line": 476, "column": 19, "nodeType": "817", "endLine": 480, "endColumn": 21}, {"ruleId": "819", "severity": 2, "message": "861", "line": 5, "column": 10, "nodeType": null, "messageId": "821", "endLine": 5, "endColumn": 19}, {"ruleId": "819", "severity": 2, "message": "862", "line": 25, "column": 3, "nodeType": null, "messageId": "821", "endLine": 25, "endColumn": 14}, {"ruleId": "819", "severity": 2, "message": "863", "line": 26, "column": 3, "nodeType": null, "messageId": "821", "endLine": 26, "endColumn": 14}, {"ruleId": "819", "severity": 2, "message": "864", "line": 28, "column": 3, "nodeType": null, "messageId": "821", "endLine": 28, "endColumn": 10}, {"ruleId": "815", "severity": 2, "message": "865", "line": 144, "column": 11, "nodeType": "817", "endLine": 147, "endColumn": 12}, {"ruleId": "815", "severity": 2, "message": "865", "line": 144, "column": 11, "nodeType": "817", "endLine": 147, "endColumn": 12}, {"ruleId": "815", "severity": 2, "message": "865", "line": 144, "column": 11, "nodeType": "817", "endLine": 147, "endColumn": 12}, {"ruleId": "815", "severity": 2, "message": "865", "line": 144, "column": 11, "nodeType": "817", "endLine": 147, "endColumn": 12}, {"ruleId": "815", "severity": 2, "message": "865", "line": 144, "column": 11, "nodeType": "817", "endLine": 147, "endColumn": 12}, {"ruleId": "815", "severity": 2, "message": "865", "line": 144, "column": 11, "nodeType": "817", "endLine": 147, "endColumn": 12}, {"ruleId": "819", "severity": 2, "message": "840", "line": 8, "column": 47, "nodeType": null, "messageId": "821", "endLine": 8, "endColumn": 53}, {"ruleId": "815", "severity": 2, "message": "816", "line": 66, "column": 15, "nodeType": "817", "endLine": 69, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 66, "column": 15, "nodeType": "817", "endLine": 69, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 66, "column": 15, "nodeType": "817", "endLine": 69, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 66, "column": 15, "nodeType": "817", "endLine": 69, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 66, "column": 15, "nodeType": "817", "endLine": 69, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 66, "column": 15, "nodeType": "817", "endLine": 69, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 66, "column": 15, "nodeType": "817", "endLine": 69, "endColumn": 16}, {"ruleId": "866", "severity": 2, "message": "867", "line": 127, "column": 25, "nodeType": "868", "messageId": "869", "suggestions": "870"}, {"ruleId": "866", "severity": 2, "message": "867", "line": 127, "column": 28, "nodeType": "868", "messageId": "869", "suggestions": "871"}, {"ruleId": "866", "severity": 2, "message": "867", "line": 127, "column": 30, "nodeType": "868", "messageId": "869", "suggestions": "872"}, {"ruleId": "866", "severity": 2, "message": "867", "line": 127, "column": 34, "nodeType": "868", "messageId": "869", "suggestions": "873"}, {"ruleId": "866", "severity": 2, "message": "867", "line": 219, "column": 23, "nodeType": "868", "messageId": "869", "suggestions": "874"}, {"ruleId": "866", "severity": 2, "message": "867", "line": 219, "column": 26, "nodeType": "868", "messageId": "869", "suggestions": "875"}, {"ruleId": "815", "severity": 2, "message": "818", "line": 274, "column": 22, "nodeType": "817", "endLine": 274, "endColumn": 41}, {"ruleId": "815", "severity": 2, "message": "818", "line": 274, "column": 22, "nodeType": "817", "endLine": 274, "endColumn": 41}, {"ruleId": "815", "severity": 2, "message": "818", "line": 274, "column": 22, "nodeType": "817", "endLine": 274, "endColumn": 41}, {"ruleId": "815", "severity": 2, "message": "818", "line": 274, "column": 22, "nodeType": "817", "endLine": 274, "endColumn": 41}, {"ruleId": "815", "severity": 2, "message": "818", "line": 274, "column": 22, "nodeType": "817", "endLine": 274, "endColumn": 41}, {"ruleId": "815", "severity": 2, "message": "818", "line": 274, "column": 22, "nodeType": "817", "endLine": 274, "endColumn": 41}, {"ruleId": "819", "severity": 2, "message": "845", "line": 27, "column": 14, "nodeType": null, "messageId": "821", "endLine": 27, "endColumn": 19}, {"ruleId": "819", "severity": 2, "message": "845", "line": 46, "column": 14, "nodeType": null, "messageId": "821", "endLine": 46, "endColumn": 19}, {"ruleId": "849", "severity": 2, "message": "850", "line": 63, "column": 42, "nodeType": "851", "messageId": "852", "endLine": 63, "endColumn": 45, "suggestions": "876"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 64, "column": 42, "nodeType": "851", "messageId": "852", "endLine": 64, "endColumn": 45, "suggestions": "877"}, {"ruleId": "819", "severity": 2, "message": "845", "line": 81, "column": 14, "nodeType": null, "messageId": "821", "endLine": 81, "endColumn": 19}, {"ruleId": "819", "severity": 2, "message": "845", "line": 73, "column": 12, "nodeType": null, "messageId": "821", "endLine": 73, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "845", "line": 66, "column": 12, "nodeType": null, "messageId": "821", "endLine": 66, "endColumn": 17}, {"ruleId": "815", "severity": 2, "message": "816", "line": 147, "column": 15, "nodeType": "817", "endLine": 150, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 147, "column": 15, "nodeType": "817", "endLine": 150, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 147, "column": 15, "nodeType": "817", "endLine": 150, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 147, "column": 15, "nodeType": "817", "endLine": 150, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 147, "column": 15, "nodeType": "817", "endLine": 150, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 147, "column": 15, "nodeType": "817", "endLine": 150, "endColumn": 16}, {"ruleId": "815", "severity": 2, "message": "816", "line": 147, "column": 15, "nodeType": "817", "endLine": 150, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 20, "column": 18, "nodeType": "851", "messageId": "852", "endLine": 20, "endColumn": 21, "suggestions": "878"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 56, "column": 22, "nodeType": "851", "messageId": "852", "endLine": 56, "endColumn": 25, "suggestions": "879"}, {"ruleId": "819", "severity": 2, "message": "880", "line": 8, "column": 27, "nodeType": null, "messageId": "821", "endLine": 8, "endColumn": 34}, {"ruleId": "849", "severity": 2, "message": "850", "line": 96, "column": 23, "nodeType": "851", "messageId": "852", "endLine": 96, "endColumn": 26, "suggestions": "881"}, {"ruleId": "819", "severity": 2, "message": "882", "line": 30, "column": 13, "nodeType": null, "messageId": "821", "endLine": 30, "endColumn": 26}, {"ruleId": "819", "severity": 2, "message": "841", "line": 5, "column": 8, "nodeType": null, "messageId": "821", "endLine": 5, "endColumn": 12}, {"ruleId": "819", "severity": 2, "message": "883", "line": 92, "column": 11, "nodeType": null, "messageId": "821", "endLine": 92, "endColumn": 14}, {"ruleId": "849", "severity": 2, "message": "850", "line": 162, "column": 25, "nodeType": "851", "messageId": "852", "endLine": 162, "endColumn": 28, "suggestions": "884"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 184, "column": 20, "nodeType": "851", "messageId": "852", "endLine": 184, "endColumn": 23, "suggestions": "885"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 216, "column": 28, "nodeType": "851", "messageId": "852", "endLine": 216, "endColumn": 31, "suggestions": "886"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 244, "column": 19, "nodeType": "851", "messageId": "852", "endLine": 244, "endColumn": 22, "suggestions": "887"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 245, "column": 56, "nodeType": "851", "messageId": "852", "endLine": 245, "endColumn": 59, "suggestions": "888"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 245, "column": 79, "nodeType": "851", "messageId": "852", "endLine": 245, "endColumn": 82, "suggestions": "889"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 23, "column": 18, "nodeType": "851", "messageId": "852", "endLine": 23, "endColumn": 21, "suggestions": "890"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 67, "column": 22, "nodeType": "851", "messageId": "852", "endLine": 67, "endColumn": 25, "suggestions": "891"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 167, "column": 19, "nodeType": "851", "messageId": "852", "endLine": 167, "endColumn": 22, "suggestions": "892"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 172, "column": 70, "nodeType": "851", "messageId": "852", "endLine": 172, "endColumn": 73, "suggestions": "893"}, {"ruleId": "819", "severity": 2, "message": "845", "line": 59, "column": 14, "nodeType": null, "messageId": "821", "endLine": 59, "endColumn": 19}, {"ruleId": "849", "severity": 2, "message": "850", "line": 41, "column": 18, "nodeType": "851", "messageId": "852", "endLine": 41, "endColumn": 21, "suggestions": "894"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 54, "column": 22, "nodeType": "851", "messageId": "852", "endLine": 54, "endColumn": 25, "suggestions": "895"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 91, "column": 52, "nodeType": "851", "messageId": "852", "endLine": 91, "endColumn": 55, "suggestions": "896"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 92, "column": 52, "nodeType": "851", "messageId": "852", "endLine": 92, "endColumn": 55, "suggestions": "897"}, {"ruleId": "819", "severity": 2, "message": "830", "line": 64, "column": 14, "nodeType": null, "messageId": "821", "endLine": 64, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "898", "line": 32, "column": 9, "nodeType": null, "messageId": "821", "endLine": 32, "endColumn": 22}, {"ruleId": "819", "severity": 2, "message": "825", "line": 24, "column": 9, "nodeType": null, "messageId": "821", "endLine": 24, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "899", "line": 26, "column": 9, "nodeType": null, "messageId": "821", "endLine": 26, "endColumn": 15}, {"ruleId": "819", "severity": 2, "message": "845", "line": 51, "column": 14, "nodeType": null, "messageId": "821", "endLine": 51, "endColumn": 19}, {"ruleId": "819", "severity": 2, "message": "845", "line": 91, "column": 14, "nodeType": null, "messageId": "821", "endLine": 91, "endColumn": 19}, {"ruleId": "819", "severity": 2, "message": "845", "line": 118, "column": 14, "nodeType": null, "messageId": "821", "endLine": 118, "endColumn": 19}, {"ruleId": "819", "severity": 2, "message": "899", "line": 18, "column": 9, "nodeType": null, "messageId": "821", "endLine": 18, "endColumn": 15}, {"ruleId": "838", "severity": 1, "message": "839", "line": 66, "column": 13, "nodeType": "817", "endLine": 70, "endColumn": 15}, {"ruleId": "838", "severity": 1, "message": "839", "line": 98, "column": 21, "nodeType": "817", "endLine": 102, "endColumn": 23}, {"ruleId": "819", "severity": 2, "message": "825", "line": 29, "column": 9, "nodeType": null, "messageId": "821", "endLine": 29, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "899", "line": 31, "column": 9, "nodeType": null, "messageId": "821", "endLine": 31, "endColumn": 15}, {"ruleId": "819", "severity": 2, "message": "825", "line": 38, "column": 9, "nodeType": null, "messageId": "821", "endLine": 38, "endColumn": 17}, {"ruleId": "819", "severity": 2, "message": "899", "line": 40, "column": 9, "nodeType": null, "messageId": "821", "endLine": 40, "endColumn": 15}, {"ruleId": "819", "severity": 2, "message": "840", "line": 12, "column": 44, "nodeType": null, "messageId": "821", "endLine": 12, "endColumn": 50}, {"ruleId": "826", "severity": 1, "message": "900", "line": 41, "column": 6, "nodeType": "828", "endLine": 41, "endColumn": 49, "suggestions": "901"}, {"ruleId": "819", "severity": 2, "message": "902", "line": 19, "column": 3, "nodeType": null, "messageId": "821", "endLine": 19, "endColumn": 12}, {"ruleId": "819", "severity": 2, "message": "903", "line": 68, "column": 3, "nodeType": null, "messageId": "821", "endLine": 68, "endColumn": 15}, {"ruleId": "819", "severity": 2, "message": "904", "line": 74, "column": 17, "nodeType": null, "messageId": "821", "endLine": 74, "endColumn": 25}, {"ruleId": "819", "severity": 2, "message": "905", "line": 77, "column": 9, "nodeType": null, "messageId": "821", "endLine": 77, "endColumn": 22}, {"ruleId": "819", "severity": 2, "message": "845", "line": 96, "column": 14, "nodeType": null, "messageId": "821", "endLine": 96, "endColumn": 19}, {"ruleId": "819", "severity": 2, "message": "841", "line": 8, "column": 10, "nodeType": null, "messageId": "821", "endLine": 8, "endColumn": 14}, {"ruleId": "819", "severity": 2, "message": "906", "line": 9, "column": 18, "nodeType": null, "messageId": "821", "endLine": 9, "endColumn": 24}, {"ruleId": "819", "severity": 2, "message": "907", "line": 9, "column": 26, "nodeType": null, "messageId": "821", "endLine": 9, "endColumn": 30}, {"ruleId": "819", "severity": 2, "message": "908", "line": 9, "column": 32, "nodeType": null, "messageId": "821", "endLine": 9, "endColumn": 36}, {"ruleId": "819", "severity": 2, "message": "909", "line": 9, "column": 38, "nodeType": null, "messageId": "821", "endLine": 9, "endColumn": 45}, {"ruleId": "819", "severity": 2, "message": "910", "line": 9, "column": 47, "nodeType": null, "messageId": "821", "endLine": 9, "endColumn": 55}, {"ruleId": "819", "severity": 2, "message": "840", "line": 29, "column": 3, "nodeType": null, "messageId": "821", "endLine": 29, "endColumn": 9}, {"ruleId": "819", "severity": 2, "message": "911", "line": 38, "column": 28, "nodeType": null, "messageId": "821", "endLine": 38, "endColumn": 47}, {"ruleId": "819", "severity": 2, "message": "912", "line": 39, "column": 18, "nodeType": null, "messageId": "821", "endLine": 39, "endColumn": 27}, {"ruleId": "819", "severity": 2, "message": "913", "line": 40, "column": 20, "nodeType": null, "messageId": "821", "endLine": 40, "endColumn": 31}, {"ruleId": "849", "severity": 2, "message": "850", "line": 50, "column": 33, "nodeType": "851", "messageId": "852", "endLine": 50, "endColumn": 36, "suggestions": "914"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 63, "column": 33, "nodeType": "851", "messageId": "852", "endLine": 63, "endColumn": 36, "suggestions": "915"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 185, "column": 22, "nodeType": "851", "messageId": "852", "endLine": 185, "endColumn": 25, "suggestions": "916"}, {"ruleId": "819", "severity": 2, "message": "917", "line": 35, "column": 61, "nodeType": null, "messageId": "821", "endLine": 35, "endColumn": 71}, {"ruleId": "838", "severity": 1, "message": "839", "line": 315, "column": 21, "nodeType": "817", "endLine": 319, "endColumn": 23}, {"ruleId": "819", "severity": 2, "message": "918", "line": 7, "column": 27, "nodeType": null, "messageId": "821", "endLine": 7, "endColumn": 34}, {"ruleId": "819", "severity": 2, "message": "919", "line": 7, "column": 36, "nodeType": null, "messageId": "821", "endLine": 7, "endColumn": 46}, {"ruleId": "826", "severity": 1, "message": "920", "line": 64, "column": 6, "nodeType": "828", "endLine": 64, "endColumn": 14, "suggestions": "921"}, {"ruleId": "838", "severity": 1, "message": "839", "line": 165, "column": 13, "nodeType": "817", "endLine": 169, "endColumn": 15}, {"ruleId": "819", "severity": 2, "message": "898", "line": 39, "column": 9, "nodeType": null, "messageId": "821", "endLine": 39, "endColumn": 22}, {"ruleId": "826", "severity": 1, "message": "922", "line": 47, "column": 6, "nodeType": "828", "endLine": 47, "endColumn": 42, "suggestions": "923"}, {"ruleId": "819", "severity": 2, "message": "924", "line": 28, "column": 16, "nodeType": null, "messageId": "821", "endLine": 28, "endColumn": 23}, {"ruleId": "826", "severity": 1, "message": "925", "line": 40, "column": 6, "nodeType": "828", "endLine": 40, "endColumn": 21, "suggestions": "926"}, {"ruleId": "819", "severity": 2, "message": "830", "line": 55, "column": 14, "nodeType": null, "messageId": "821", "endLine": 55, "endColumn": 17}, {"ruleId": "838", "severity": 1, "message": "839", "line": 70, "column": 19, "nodeType": "817", "endLine": 74, "endColumn": 21}, {"ruleId": "838", "severity": 1, "message": "839", "line": 199, "column": 27, "nodeType": "817", "endLine": 203, "endColumn": 29}, {"ruleId": "819", "severity": 2, "message": "898", "line": 40, "column": 9, "nodeType": null, "messageId": "821", "endLine": 40, "endColumn": 22}, {"ruleId": "838", "severity": 1, "message": "839", "line": 180, "column": 7, "nodeType": "817", "endLine": 189, "endColumn": 9}, {"ruleId": "819", "severity": 2, "message": "927", "line": 37, "column": 10, "nodeType": null, "messageId": "821", "endLine": 37, "endColumn": 18}, {"ruleId": "928", "severity": 1, "message": "929", "line": 78, "column": 9, "nodeType": "817", "endLine": 82, "endColumn": 11}, {"ruleId": "928", "severity": 1, "message": "929", "line": 92, "column": 7, "nodeType": "817", "endLine": 96, "endColumn": 9}, {"ruleId": "826", "severity": 1, "message": "930", "line": 176, "column": 6, "nodeType": "828", "endLine": 176, "endColumn": 15, "suggestions": "931"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 279, "column": 16, "nodeType": "851", "messageId": "852", "endLine": 279, "endColumn": 19, "suggestions": "932"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 6, "column": 34, "nodeType": "851", "messageId": "852", "endLine": 6, "endColumn": 37, "suggestions": "933"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 292, "column": 51, "nodeType": "851", "messageId": "852", "endLine": 292, "endColumn": 54, "suggestions": "934"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 293, "column": 25, "nodeType": "851", "messageId": "852", "endLine": 293, "endColumn": 28, "suggestions": "935"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 298, "column": 27, "nodeType": "851", "messageId": "852", "endLine": 298, "endColumn": 30, "suggestions": "936"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 299, "column": 25, "nodeType": "851", "messageId": "852", "endLine": 299, "endColumn": 28, "suggestions": "937"}, {"ruleId": "819", "severity": 2, "message": "938", "line": 7, "column": 10, "nodeType": null, "messageId": "821", "endLine": 7, "endColumn": 24}, {"ruleId": "849", "severity": 2, "message": "850", "line": 75, "column": 60, "nodeType": "851", "messageId": "852", "endLine": 75, "endColumn": 63, "suggestions": "939"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 155, "column": 31, "nodeType": "851", "messageId": "852", "endLine": 155, "endColumn": 34, "suggestions": "940"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 161, "column": 26, "nodeType": "851", "messageId": "852", "endLine": 161, "endColumn": 29, "suggestions": "941"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 162, "column": 26, "nodeType": "851", "messageId": "852", "endLine": 162, "endColumn": 29, "suggestions": "942"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 85, "column": 35, "nodeType": "851", "messageId": "852", "endLine": 85, "endColumn": 38, "suggestions": "943"}, {"ruleId": "819", "severity": 2, "message": "944", "line": 1, "column": 8, "nodeType": null, "messageId": "821", "endLine": 1, "endColumn": 16}, "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", "Do not use an `<a>` element to navigate to `/contact/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'pathname' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["945"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'Eye' is defined but never used.", "'tProfile' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["946"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "'locale' is defined but never used.", "'Tool' is defined but never used.", "'ArrowLeft' is defined but never used.", "'isValidCategory' is defined but never used.", "'getLocale' is defined but never used.", "'error' is defined but never used.", "Do not use an `<a>` element to navigate to `/categories/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "'MapPin' is defined but never used.", "Do not use an `<a>` element to navigate to `/faq/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["947", "948"], "React Hook useEffect has a missing dependency: 'createPaymentIntent'. Either include it or remove the dependency array.", ["949"], ["950", "951"], "'User' is defined but never used.", "'Edit' is defined but never used.", ["952", "953"], "'Mail' is defined but never used.", "'useLocale' is defined but never used.", "'minFreeDate' is defined but never used.", "'minPaidDate' is defined but never used.", "'orderId' is defined but never used.", "Do not use an `<a>` element to navigate to `/profile/submitted/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["954", "955", "956", "957"], ["958", "959", "960", "961"], ["962", "963", "964", "965"], ["966", "967", "968", "969"], ["970", "971", "972", "973"], ["974", "975", "976", "977"], ["978", "979"], ["980", "981"], ["982", "983"], ["984", "985"], "'request' is defined but never used.", ["986", "987"], "'paymentMethod' is assigned a value but never used.", "'now' is assigned a value but never used.", ["988", "989"], ["990", "991"], ["992", "993"], ["994", "995"], ["996", "997"], ["998", "999"], ["1000", "1001"], ["1002", "1003"], ["1004", "1005"], ["1006", "1007"], ["1008", "1009"], ["1010", "1011"], ["1012", "1013"], ["1014", "1015"], "'currentLocale' is assigned a value but never used.", "'locale' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["1016"], "'RefreshCw' is defined but never used.", "'initialStats' is defined but never used.", "'setTools' is assigned a value but never used.", "'handleReapply' is assigned a value but never used.", "'Filter' is defined but never used.", "'Grid' is defined but never used.", "'List' is defined but never used.", "'SortAsc' is defined but never used.", "'SortDesc' is defined but never used.", "'setSelectedCategory' is assigned a value but never used.", "'setSortBy' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", ["1017", "1018"], ["1019", "1020"], ["1021", "1022"], "'tagOptions' is defined but never used.", "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["1023"], "React Hook useEffect has a missing dependency: 'initializeToolState'. Either include it or remove the dependency array.", ["1024"], "'setTool' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchRelatedTools'. Either include it or remove the dependency array.", ["1025"], "'hasError' is assigned a value but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "React Hook useEffect has missing dependencies: 'refreshToolState' and 'toolStates'. Either include them or remove the dependency array.", ["1026"], ["1027", "1028"], ["1029", "1030"], ["1031", "1032"], ["1033", "1034"], ["1035", "1036"], ["1037", "1038"], "'getNextAuthUrl' is defined but never used.", ["1039", "1040"], ["1041", "1042"], ["1043", "1044"], ["1045", "1046"], ["1047", "1048"], "'mongoose' is defined but never used.", {"desc": "1049", "fix": "1050"}, {"desc": "1051", "fix": "1052"}, {"messageId": "1053", "fix": "1054", "desc": "1055"}, {"messageId": "1056", "fix": "1057", "desc": "1058"}, {"desc": "1059", "fix": "1060"}, {"messageId": "1053", "fix": "1061", "desc": "1055"}, {"messageId": "1056", "fix": "1062", "desc": "1058"}, {"messageId": "1053", "fix": "1063", "desc": "1055"}, {"messageId": "1056", "fix": "1064", "desc": "1058"}, {"messageId": "1065", "data": "1066", "fix": "1067", "desc": "1068"}, {"messageId": "1065", "data": "1069", "fix": "1070", "desc": "1071"}, {"messageId": "1065", "data": "1072", "fix": "1073", "desc": "1074"}, {"messageId": "1065", "data": "1075", "fix": "1076", "desc": "1077"}, {"messageId": "1065", "data": "1078", "fix": "1079", "desc": "1068"}, {"messageId": "1065", "data": "1080", "fix": "1081", "desc": "1071"}, {"messageId": "1065", "data": "1082", "fix": "1083", "desc": "1074"}, {"messageId": "1065", "data": "1084", "fix": "1085", "desc": "1077"}, {"messageId": "1065", "data": "1086", "fix": "1087", "desc": "1068"}, {"messageId": "1065", "data": "1088", "fix": "1089", "desc": "1071"}, {"messageId": "1065", "data": "1090", "fix": "1091", "desc": "1074"}, {"messageId": "1065", "data": "1092", "fix": "1093", "desc": "1077"}, {"messageId": "1065", "data": "1094", "fix": "1095", "desc": "1068"}, {"messageId": "1065", "data": "1096", "fix": "1097", "desc": "1071"}, {"messageId": "1065", "data": "1098", "fix": "1099", "desc": "1074"}, {"messageId": "1065", "data": "1100", "fix": "1101", "desc": "1077"}, {"messageId": "1065", "data": "1102", "fix": "1103", "desc": "1068"}, {"messageId": "1065", "data": "1104", "fix": "1105", "desc": "1071"}, {"messageId": "1065", "data": "1106", "fix": "1107", "desc": "1074"}, {"messageId": "1065", "data": "1108", "fix": "1109", "desc": "1077"}, {"messageId": "1065", "data": "1110", "fix": "1111", "desc": "1068"}, {"messageId": "1065", "data": "1112", "fix": "1113", "desc": "1071"}, {"messageId": "1065", "data": "1114", "fix": "1115", "desc": "1074"}, {"messageId": "1065", "data": "1116", "fix": "1117", "desc": "1077"}, {"messageId": "1053", "fix": "1118", "desc": "1055"}, {"messageId": "1056", "fix": "1119", "desc": "1058"}, {"messageId": "1053", "fix": "1120", "desc": "1055"}, {"messageId": "1056", "fix": "1121", "desc": "1058"}, {"messageId": "1053", "fix": "1122", "desc": "1055"}, {"messageId": "1056", "fix": "1123", "desc": "1058"}, {"messageId": "1053", "fix": "1124", "desc": "1055"}, {"messageId": "1056", "fix": "1125", "desc": "1058"}, {"messageId": "1053", "fix": "1126", "desc": "1055"}, {"messageId": "1056", "fix": "1127", "desc": "1058"}, {"messageId": "1053", "fix": "1128", "desc": "1055"}, {"messageId": "1056", "fix": "1129", "desc": "1058"}, {"messageId": "1053", "fix": "1130", "desc": "1055"}, {"messageId": "1056", "fix": "1131", "desc": "1058"}, {"messageId": "1053", "fix": "1132", "desc": "1055"}, {"messageId": "1056", "fix": "1133", "desc": "1058"}, {"messageId": "1053", "fix": "1134", "desc": "1055"}, {"messageId": "1056", "fix": "1135", "desc": "1058"}, {"messageId": "1053", "fix": "1136", "desc": "1055"}, {"messageId": "1056", "fix": "1137", "desc": "1058"}, {"messageId": "1053", "fix": "1138", "desc": "1055"}, {"messageId": "1056", "fix": "1139", "desc": "1058"}, {"messageId": "1053", "fix": "1140", "desc": "1055"}, {"messageId": "1056", "fix": "1141", "desc": "1058"}, {"messageId": "1053", "fix": "1142", "desc": "1055"}, {"messageId": "1056", "fix": "1143", "desc": "1058"}, {"messageId": "1053", "fix": "1144", "desc": "1055"}, {"messageId": "1056", "fix": "1145", "desc": "1058"}, {"messageId": "1053", "fix": "1146", "desc": "1055"}, {"messageId": "1056", "fix": "1147", "desc": "1058"}, {"messageId": "1053", "fix": "1148", "desc": "1055"}, {"messageId": "1056", "fix": "1149", "desc": "1058"}, {"messageId": "1053", "fix": "1150", "desc": "1055"}, {"messageId": "1056", "fix": "1151", "desc": "1058"}, {"messageId": "1053", "fix": "1152", "desc": "1055"}, {"messageId": "1056", "fix": "1153", "desc": "1058"}, {"messageId": "1053", "fix": "1154", "desc": "1055"}, {"messageId": "1056", "fix": "1155", "desc": "1058"}, {"desc": "1156", "fix": "1157"}, {"messageId": "1053", "fix": "1158", "desc": "1055"}, {"messageId": "1056", "fix": "1159", "desc": "1058"}, {"messageId": "1053", "fix": "1160", "desc": "1055"}, {"messageId": "1056", "fix": "1161", "desc": "1058"}, {"messageId": "1053", "fix": "1162", "desc": "1055"}, {"messageId": "1056", "fix": "1163", "desc": "1058"}, {"desc": "1164", "fix": "1165"}, {"desc": "1166", "fix": "1167"}, {"desc": "1168", "fix": "1169"}, {"desc": "1170", "fix": "1171"}, {"messageId": "1053", "fix": "1172", "desc": "1055"}, {"messageId": "1056", "fix": "1173", "desc": "1058"}, {"messageId": "1053", "fix": "1174", "desc": "1055"}, {"messageId": "1056", "fix": "1175", "desc": "1058"}, {"messageId": "1053", "fix": "1176", "desc": "1055"}, {"messageId": "1056", "fix": "1177", "desc": "1058"}, {"messageId": "1053", "fix": "1178", "desc": "1055"}, {"messageId": "1056", "fix": "1179", "desc": "1058"}, {"messageId": "1053", "fix": "1180", "desc": "1055"}, {"messageId": "1056", "fix": "1181", "desc": "1058"}, {"messageId": "1053", "fix": "1182", "desc": "1055"}, {"messageId": "1056", "fix": "1183", "desc": "1058"}, {"messageId": "1053", "fix": "1184", "desc": "1055"}, {"messageId": "1056", "fix": "1185", "desc": "1058"}, {"messageId": "1053", "fix": "1186", "desc": "1055"}, {"messageId": "1056", "fix": "1187", "desc": "1058"}, {"messageId": "1053", "fix": "1188", "desc": "1055"}, {"messageId": "1056", "fix": "1189", "desc": "1058"}, {"messageId": "1053", "fix": "1190", "desc": "1055"}, {"messageId": "1056", "fix": "1191", "desc": "1058"}, {"messageId": "1053", "fix": "1192", "desc": "1055"}, {"messageId": "1056", "fix": "1193", "desc": "1058"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "1194", "text": "1195"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "1196", "text": "1197"}, "suggestUnknown", {"range": "1198", "text": "1199"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1200", "text": "1201"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [createPaymentIntent]", {"range": "1202", "text": "1203"}, {"range": "1204", "text": "1199"}, {"range": "1205", "text": "1201"}, {"range": "1206", "text": "1199"}, {"range": "1207", "text": "1201"}, "replaceWithAlt", {"alt": "1208"}, {"range": "1209", "text": "1210"}, "Replace with `&quot;`.", {"alt": "1211"}, {"range": "1212", "text": "1213"}, "Replace with `&ldquo;`.", {"alt": "1214"}, {"range": "1215", "text": "1216"}, "Replace with `&#34;`.", {"alt": "1217"}, {"range": "1218", "text": "1219"}, "Replace with `&rdquo;`.", {"alt": "1208"}, {"range": "1220", "text": "1221"}, {"alt": "1211"}, {"range": "1222", "text": "1223"}, {"alt": "1214"}, {"range": "1224", "text": "1225"}, {"alt": "1217"}, {"range": "1226", "text": "1227"}, {"alt": "1208"}, {"range": "1228", "text": "1229"}, {"alt": "1211"}, {"range": "1230", "text": "1231"}, {"alt": "1214"}, {"range": "1232", "text": "1233"}, {"alt": "1217"}, {"range": "1234", "text": "1235"}, {"alt": "1208"}, {"range": "1236", "text": "1237"}, {"alt": "1211"}, {"range": "1238", "text": "1239"}, {"alt": "1214"}, {"range": "1240", "text": "1241"}, {"alt": "1217"}, {"range": "1242", "text": "1243"}, {"alt": "1208"}, {"range": "1244", "text": "1245"}, {"alt": "1211"}, {"range": "1246", "text": "1247"}, {"alt": "1214"}, {"range": "1248", "text": "1249"}, {"alt": "1217"}, {"range": "1250", "text": "1251"}, {"alt": "1208"}, {"range": "1252", "text": "1253"}, {"alt": "1211"}, {"range": "1254", "text": "1255"}, {"alt": "1214"}, {"range": "1256", "text": "1257"}, {"alt": "1217"}, {"range": "1258", "text": "1259"}, {"range": "1260", "text": "1199"}, {"range": "1261", "text": "1201"}, {"range": "1262", "text": "1199"}, {"range": "1263", "text": "1201"}, {"range": "1264", "text": "1199"}, {"range": "1265", "text": "1201"}, {"range": "1266", "text": "1199"}, {"range": "1267", "text": "1201"}, {"range": "1268", "text": "1199"}, {"range": "1269", "text": "1201"}, {"range": "1270", "text": "1199"}, {"range": "1271", "text": "1201"}, {"range": "1272", "text": "1199"}, {"range": "1273", "text": "1201"}, {"range": "1274", "text": "1199"}, {"range": "1275", "text": "1201"}, {"range": "1276", "text": "1199"}, {"range": "1277", "text": "1201"}, {"range": "1278", "text": "1199"}, {"range": "1279", "text": "1201"}, {"range": "1280", "text": "1199"}, {"range": "1281", "text": "1201"}, {"range": "1282", "text": "1199"}, {"range": "1283", "text": "1201"}, {"range": "1284", "text": "1199"}, {"range": "1285", "text": "1201"}, {"range": "1286", "text": "1199"}, {"range": "1287", "text": "1201"}, {"range": "1288", "text": "1199"}, {"range": "1289", "text": "1201"}, {"range": "1290", "text": "1199"}, {"range": "1291", "text": "1201"}, {"range": "1292", "text": "1199"}, {"range": "1293", "text": "1201"}, {"range": "1294", "text": "1199"}, {"range": "1295", "text": "1201"}, {"range": "1296", "text": "1199"}, {"range": "1297", "text": "1201"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "1298", "text": "1299"}, {"range": "1300", "text": "1199"}, {"range": "1301", "text": "1201"}, {"range": "1302", "text": "1199"}, {"range": "1303", "text": "1201"}, {"range": "1304", "text": "1199"}, {"range": "1305", "text": "1201"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "1306", "text": "1307"}, "Update the dependencies array to be: [toolId, initialLikes, initialLiked, initializeToolState]", {"range": "1308", "text": "1309"}, "Update the dependencies array to be: [fetchRelatedTools, tool.category]", {"range": "1310", "text": "1311"}, "Update the dependencies array to be: [refreshToolState, session, toolStates]", {"range": "1312", "text": "1313"}, {"range": "1314", "text": "1199"}, {"range": "1315", "text": "1201"}, {"range": "1316", "text": "1199"}, {"range": "1317", "text": "1201"}, {"range": "1318", "text": "1199"}, {"range": "1319", "text": "1201"}, {"range": "1320", "text": "1199"}, {"range": "1321", "text": "1201"}, {"range": "1322", "text": "1199"}, {"range": "1323", "text": "1201"}, {"range": "1324", "text": "1199"}, {"range": "1325", "text": "1201"}, {"range": "1326", "text": "1199"}, {"range": "1327", "text": "1201"}, {"range": "1328", "text": "1199"}, {"range": "1329", "text": "1201"}, {"range": "1330", "text": "1199"}, {"range": "1331", "text": "1201"}, {"range": "1332", "text": "1199"}, {"range": "1333", "text": "1201"}, {"range": "1334", "text": "1199"}, {"range": "1335", "text": "1201"}, [950, 961], "[fetchStats, timeRange]", [1532, 1546], "[fetchTools, statusFilter]", [628, 631], "unknown", [628, 631], "never", [1026, 1028], "[createPaymentIntent]", [1559, 1562], [1559, 1562], [1777, 1780], [1777, 1780], "&quot;", [4472, 4520], "AI工具导航（以下简称&quot;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&ldquo;", [4472, 4520], "AI工具导航（以下简称&ldquo;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&#34;", [4472, 4520], "AI工具导航（以下简称&#34;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&rdquo;", [4472, 4520], "AI工具导航（以下简称&rdquo;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&quot;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&ldquo;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&#34;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&rdquo;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&quot;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&ldquo;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&#34;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&rdquo;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&quot;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&ldquo;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&#34;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&rdquo;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [6921, 6947], "我们的服务按&quot;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&ldquo;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&#34;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&rdquo;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&quot;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&ldquo;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&#34;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&rdquo;提供，不提供任何明示或暗示的保证", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [746, 749], [746, 749], [1625, 1628], [1625, 1628], [2591, 2594], [2591, 2594], [4807, 4810], [4807, 4810], [5136, 5139], [5136, 5139], [5980, 5983], [5980, 5983], [6670, 6673], [6670, 6673], [6761, 6764], [6761, 6764], [6784, 6787], [6784, 6787], [876, 879], [876, 879], [1983, 1986], [1983, 1986], [4561, 4564], [4561, 4564], [4783, 4786], [4783, 4786], [1364, 1367], [1364, 1367], [1635, 1638], [1635, 1638], [2622, 2625], [2622, 2625], [2710, 2713], [2710, 2713], [1214, 1257], "[filterTools, likedTools, searchQuery, selectedCategory]", [1561, 1564], [1561, 1564], [2024, 2027], [2024, 2027], [4765, 4768], [4765, 4768], [1768, 1776], "[fetchComments, toolId]", [1272, 1308], "[toolId, initialLikes, initialLiked, initializeToolState]", [1249, 1264], "[fetchRelatedTools, tool.category]", [4405, 4414], "[refreshToolState, session, toolStates]", [8390, 8393], [8390, 8393], [125, 128], [125, 128], [6911, 6914], [6911, 6914], [6943, 6946], [6943, 6946], [7073, 7076], [7073, 7076], [7105, 7108], [7105, 7108], [2315, 2318], [2315, 2318], [4647, 4650], [4647, 4650], [4803, 4806], [4803, 4806], [4862, 4865], [4862, 4865], [1724, 1727], [1724, 1727]]