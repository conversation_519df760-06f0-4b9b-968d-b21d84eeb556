// 国际化标签配置文件
// 支持多语言的AI工具标签配置

import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import { AVAILABLE_TAG_KEYS, TAG_KEYS_BY_CATEGORY } from './tags';

export interface TagOption {
  key: string;
  label: string;
}

export interface TagCategory {
  key: string;
  name: string;
  tags: TagOption[];
}

// 客户端钩子：获取国际化的标签选项
export function useTagOptions(): TagOption[] {
  const t = useTranslations('tags');
  
  return AVAILABLE_TAG_KEYS.map(key => ({
    key,
    label: t(key)
  }));
}

// 客户端钩子：获取按分类组织的国际化标签
export function useTagsByCategory(): TagCategory[] {
  const t = useTranslations('tags');
  const categoryT = useTranslations('tag_categories');
  
  return Object.entries(TAG_KEYS_BY_CATEGORY).map(([categoryKey, tagKeys]) => ({
    key: categoryKey,
    name: categoryT(categoryKey),
    tags: tagKeys.map(tagKey => ({
      key: tagKey,
      label: t(tagKey)
    }))
  }));
}

// 客户端钩子：获取单个标签的翻译
export function useTagLabel(tagKey: string): string {
  const t = useTranslations('tags');
  return t(tagKey) || tagKey;
}

// 客户端钩子：获取标签分类名称
export function useTagCategoryName(categoryKey: string): string {
  const t = useTranslations('tag_categories');
  return t(categoryKey) || categoryKey;
}

// 获取所有可用的标签键值（用于验证）
export function getAvailableTagKeys(): string[] {
  return AVAILABLE_TAG_KEYS;
}

// 获取所有可用的标签分类键值（用于验证）
export function getAvailableTagCategoryKeys(): string[] {
  return Object.keys(TAG_KEYS_BY_CATEGORY);
}

// 服务器端函数：获取国际化的标签选项
export async function getTagOptions(locale?: string): Promise<TagOption[]> {
  const t = await getTranslations({ locale, namespace: 'tags' });

  return AVAILABLE_TAG_KEYS.map(key => ({
    key,
    label: t(key)
  }));
}

// 服务器端函数：获取按分类组织的国际化标签
export async function getTagsByCategory(locale?: string): Promise<TagCategory[]> {
  const t = await getTranslations({ locale, namespace: 'tags' });
  const categoryT = await getTranslations({ locale, namespace: 'tag_categories' });

  return Object.entries(TAG_KEYS_BY_CATEGORY).map(([categoryKey, tagKeys]) => ({
    key: categoryKey,
    name: categoryT(categoryKey),
    tags: tagKeys.map(tagKey => ({
      key: tagKey,
      label: t(tagKey)
    }))
  }));
}

// 服务器端函数：获取单个标签的翻译
export async function getTagLabel(tagKey: string, locale?: string): Promise<string> {
  const t = await getTranslations({ locale, namespace: 'tags' });
  return t(tagKey) || tagKey;
}

// 服务器端函数：获取标签分类名称
export async function getTagCategoryName(categoryKey: string, locale?: string): Promise<string> {
  const t = await getTranslations({ locale, namespace: 'tag_categories' });
  return t(categoryKey) || categoryKey;
}
