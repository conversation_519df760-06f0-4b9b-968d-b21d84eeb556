'use client';

import React, { useState } from 'react';
import { usePathname } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import { Tag, Search, X } from 'lucide-react';
import { MAX_TAGS_COUNT } from '@/constants/tags';
import { useTagOptions } from '@/constants/tags-i18n';
import { Locale } from '@/i18n/config';

interface TagSelectorProps {
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  maxTags?: number;
  placeholder?: string;
}

export default function TagSelector({
  selectedTags,
  onTagsChange,
  maxTags = MAX_TAGS_COUNT,
  placeholder
}: TagSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  const pathname = usePathname();
  const t = useTranslations('common');
  const tagOptions = useTagOptions();

  // Extract current locale from pathname
  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;

  const toggleTag = (tagKey: string) => {
    if (selectedTags.includes(tagKey)) {
      onTagsChange(selectedTags.filter(t => t !== tagKey));
    } else if (selectedTags.length < maxTags) {
      onTagsChange([...selectedTags, tagKey]);
    }
  };

  const removeTag = (tagKey: string) => {
    onTagsChange(selectedTags.filter(t => t !== tagKey));
  };

  // 过滤标签：根据搜索词过滤，并排除已选择的标签
  const filteredTags = tagOptions.filter(tag =>
    tag.label.toLowerCase().includes(searchTerm.toLowerCase()) &&
    !selectedTags.includes(tag.key)
  );

  // 获取已选择标签的显示文本
  const getSelectedTagLabel = (tagKey: string) => {
    const tagOption = tagOptions.find(tag => tag.key === tagKey);
    return tagOption ? tagOption.label : tagKey;
  };

  return (
    <div className="space-y-4">
      {/* 标题和计数器 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">{t('select_tags')}</h3>
        <span className="text-sm text-gray-500">
          {t('selected_count', { count: selectedTags.length, max: maxTags })}
        </span>
      </div>

      {/* 已选择的标签 */}
      {selectedTags.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">{t('selected_tags')}</h4>
          <div className="flex flex-wrap gap-2">
            {selectedTags.map((tagKey) => (
              <span
                key={tagKey}
                className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800"
              >
                {getSelectedTagLabel(tagKey)}
                <button
                  type="button"
                  onClick={() => removeTag(tagKey)}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            ))}
          </div>
        </div>
      )}

      {/* 标签选择器 */}
      <div className="space-y-3">
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('select_tags_max', { max: maxTags })}
          </label>

          {/* 搜索框 */}
          <div className="relative mb-3">
            <input
              type="text"
              placeholder={placeholder || t('search_tags')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onFocus={() => setIsOpen(true)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
          </div>

          {/* 标签选择下拉框 */}
          {(isOpen || searchTerm) && (
            <div className="relative">
              <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                {filteredTags.length > 0 ? (
                  <div className="p-2">
                    <div className="grid grid-cols-1 gap-1">
                      {filteredTags.map((tag) => {
                        const isDisabled = selectedTags.length >= maxTags;

                        return (
                          <button
                            key={tag.key}
                            type="button"
                            onClick={() => {
                              toggleTag(tag.key);
                              setSearchTerm('');
                              setIsOpen(false);
                            }}
                            disabled={isDisabled}
                            className={`
                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors
                              ${isDisabled
                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                : 'hover:bg-blue-50 text-gray-700'
                              }
                            `}
                          >
                            <div className="flex items-center">
                              <Tag className="h-3 w-3 mr-2 text-gray-400" />
                              {tag.label}
                            </div>
                          </button>
                        );
                      })}
                    </div>
                    {filteredTags.length > 50 && (
                      <p className="text-xs text-gray-500 mt-2 px-3">
                        {t('found_tags', { count: filteredTags.length })}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="p-4 text-center text-gray-500 text-sm">
                    {searchTerm ? t('no_tags_found') : t('start_typing')}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 点击外部关闭下拉框 */}
      {(isOpen || searchTerm) && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => {
            setIsOpen(false);
            setSearchTerm('');
          }}
        />
      )}

      {/* 提示信息 */}
      {selectedTags.length >= maxTags && (
        <p className="text-sm text-amber-600">
          {t('max_tags_limit', { max: maxTags })}
        </p>
      )}
    </div>
  );
}
