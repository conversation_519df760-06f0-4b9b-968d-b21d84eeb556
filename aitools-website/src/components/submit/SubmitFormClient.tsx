'use client';

import React, { useState, Fragment } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import SuccessMessage from '@/components/SuccessMessage';
import LoginModal from '@/components/auth/LoginModal';
import { TOOL_PRICING_FORM_OPTIONS } from '@/constants/pricing';
import {
  Upload,
  Link as LinkIcon,
  Info
} from 'lucide-react';
import { MAX_TAGS_COUNT } from '@/constants/tags';
import TagSelector from '@/components/TagSelector';

interface CategoryOption {
  value: string;
  label: string;
}

interface TagOption {
  key: string;
  label: string;
}

interface SubmitFormClientProps {
  categoryOptions: CategoryOption[];
  tagOptions: TagOption[];
}

export default function SubmitFormClient({ categoryOptions, tagOptions }: SubmitFormClientProps) {
  const t = useTranslations('submit');
  const { data: session } = useSession();
  const router = useRouter();

  const [formData, setFormData] = useState({
    name: '',
    tagline: '',
    description: '',
    website: '',
    logoFile: null as File | null,
    category: '',
    tags: [] as string[],
    pricing: ''
  });

  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        logoFile: file
      }));

      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleTagsChange = (selectedTags: string[]) => {
    setFormData(prev => ({
      ...prev,
      tags: selectedTags
    }));
  };

  const validateForm = () => {
    const requiredFields = [
      { key: 'name', label: t('form.tool_name') },
      { key: 'tagline', label: t('form.tagline') },
      { key: 'description', label: t('form.description') },
      { key: 'websiteUrl', label: t('form.website_url') },
      { key: 'category', label: t('form.category') },
      { key: 'pricingModel', label: t('form.pricing_model') },
    ];
    const missingFields = requiredFields.filter(field => !formData[field.key as keyof typeof formData]);
    
    if (missingFields.length > 0) {
      alert(
      t('form.missing_required_fields') +
      ':\n' +
      missingFields.map(field => `- ${field.label}`).join('\n')
      );
      return false;
    }

    // 验证 logo 必填
    if (!formData.logoFile) {
      alert(t('form.logo_required'));
      return false;
    }

    if (formData.tags.length === 0) {
      alert(t('form.tags_placeholder'));
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!session?.user?.email) {
      setIsLoginModalOpen(true);
      return;
    }

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // 上传 logo 文件
      let logoUrl = '';
      if (formData.logoFile) {
        const logoFormData = new FormData();
        logoFormData.append('logo', formData.logoFile);

        const uploadResponse = await fetch('/api/upload/logo', {
          method: 'POST',
          body: logoFormData,
        });

        if (uploadResponse.ok) {
          const uploadResult = await uploadResponse.json();
          logoUrl = uploadResult.data.url;
        } else {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.message || 'Logo upload failed');
        }
      }

      // 提交工具数据
      const submitData = {
        name: formData.name,
        tagline: formData.tagline,
        description: formData.description,
        website: formData.websiteUrl,
        logoUrl: logoUrl,
        category: formData.category,
        tags: formData.tags,
        pricing: formData.pricingModel,
      };

      const response = await fetch('/api/tools/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        const result = await response.json();
        setSubmitStatus('success');
        
        // 重定向到成功页面
        setTimeout(() => {
          router.push(`/submit/success?toolId=${result.toolId}`);
        }, 2000);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Submission failed');
      }
    } catch (error) {
      console.error('Submit error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitStatus === 'success') {
    return (
      <div className="max-w-2xl mx-auto">
        <SuccessMessage message={t('form.success_message')} />
      </div>
    );
  }

  return (
    <Fragment>
      <form onSubmit={handleSubmit} className="max-w-4xl mx-auto space-y-8">
        {/* 基本信息 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <Info className="h-5 w-5 mr-2 text-blue-600" />
            {t('form.basic_info')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 工具名称 */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.tool_name')} <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder={t('form.tool_name_placeholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* 工具标语 */}
            <div>
              <label htmlFor="tagline" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.tagline')} <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="tagline"
                name="tagline"
                value={formData.tagline}
                onChange={handleInputChange}
                placeholder={t('form.tagline_placeholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* 详细描述 */}
          <div className="mt-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              {t('form.description')} <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder={t('form.description_placeholder')}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          {/* 官方网站 */}
          <div className="mt-6">
            <label htmlFor="websiteUrl" className="block text-sm font-medium text-gray-700 mb-2">
              {t('form.website_url')} <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <LinkIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="url"
                id="websiteUrl"
                name="websiteUrl"
                value={formData.websiteUrl}
                onChange={handleInputChange}
                placeholder={t('form.website_url_placeholder')}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
          </div>

          {/* Logo 上传 */}
          <div className="mt-6">
            <label htmlFor="logo" className="block text-sm font-medium text-gray-700 mb-2">
              {t('form.logo_upload')} <span className="text-red-500">*</span>
            </label>
            <div className="flex items-start space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <input
                    type="file"
                    id="logo"
                    name="logo"
                    accept="image/*"
                    onChange={handleLogoChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  {t('form.logo_upload_hint')}
                </p>
              </div>
              {logoPreview && (
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 border border-gray-300 rounded-md overflow-hidden">
                    <img
                      src={logoPreview}
                      alt={t('form.logo_preview')}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 分类和定价 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            {t('form.category_and_pricing')}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 工具分类 */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.category')} <span className="text-red-500">*</span>
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">{t('form.category_placeholder')}</option>
                {categoryOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* 价格模式 */}
            <div>
              <label htmlFor="pricingModel" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.pricing_model')} <span className="text-red-500">*</span>
              </label>
              <select
                id="pricingModel"
                name="pricingModel"
                value={formData.pricingModel}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">{t('form.pricing_placeholder')}</option>
                {TOOL_PRICING_FORM_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {t(`form.${option.value}`)}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* 选择标签 */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('form.tags')} <span className="text-red-500">*</span>
            </label>
            <TagSelector
              selectedTags={formData.tags}
              onTagsChange={handleTagsChange}
              maxTags={MAX_TAGS_COUNT}
              placeholder={t('form.tags_placeholder')}
            />
          </div>
        </div>

        {/* 提交者信息 */}
        {/* <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            {t('form.submitter_info')}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.submitter')}
              </label>
              <input
                type="text"
                value={session?.user?.name || session?.user?.email || ''}
                disabled
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.email')}
              </label>
              <input
                type="email"
                value={session?.user?.email || ''}
                disabled
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500"
              />
            </div>
          </div>
        </div> */}

        {/* 提交指南 */}
        <div className="bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">
            {t('form.guidelines_title')}
          </h3>
          <ul className="space-y-2 text-blue-800">
            <li className="flex items-start">
              <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
              {t('form.guideline_1')}
            </li>
            <li className="flex items-start">
              <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
              {t('form.guideline_2')}
            </li>
            <li className="flex items-start">
              <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
              {t('form.guideline_3')}
            </li>
            <li className="flex items-start">
              <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
              {t('form.guideline_4')}
            </li>
            <li className="flex items-start">
              <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
              {t('form.guideline_5')}
            </li>
          </ul>
        </div>

        {/* 提交按钮 */}
        <div className="flex justify-center">
          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <Fragment>
                <LoadingSpinner size="sm" className="mr-2" />
                {t('form.submitting')}
              </Fragment>
            ) : (
              <Fragment>
                <Upload className="h-5 w-5 mr-2" />
                {t('form.submit_button')}
              </Fragment>
            )}
          </button>
        </div>

        {submitStatus === 'error' && (
          <div className="mt-4">
            <ErrorMessage message={t('form.error_message')} />
          </div>
        )}
      </form>

      {/* 登录模态框 */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </Fragment>
  );
}
