import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/mongodb';
import Order from '@/models/Order';
import User from '@/models/User';
import { authOptions } from '@/lib/auth';
import { createPaymentIntent, getOrCreateStripeCustomer, stripe } from '@/lib/stripe';
import mongoose from 'mongoose';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

// POST /api/stripe/create-payment-intent - 创建Stripe支付意图
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    await dbConnect();

    const { orderId } = await request.json();

    if (!mongoose.Types.ObjectId.isValid(orderId)) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.not_found') },
        { status: 404 }
      );
    }

    const order = await Order.findById(orderId);
    if (!order) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.not_found') },
        { status: 404 }
      );
    }

    // 检查订单所有权
    if (order.userId.toString() !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.forbidden') },
        { status: 403 }
      );
    }

    // 检查订单状态
    if (order.status !== 'pending') {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'payment.payment_failed') },
        { status: 400 }
      );
    }

    // 如果已经有支付意图ID，先检查状态
    if (order.stripePaymentIntentId) {
      try {
        const existingPaymentIntent = await stripe.paymentIntents.retrieve(order.stripePaymentIntentId);
        
        if (existingPaymentIntent.status === 'succeeded') {
          return NextResponse.json(
            { success: false, message: getApiMessage(locale, 'payment.payment_success') },
            { status: 400 }
          );
        }
        
        if (existingPaymentIntent.status === 'requires_payment_method' || 
            existingPaymentIntent.status === 'requires_confirmation') {
          // 返回现有的支付意图
          return NextResponse.json({
            success: true,
            data: {
              clientSecret: existingPaymentIntent.client_secret,
              paymentIntentId: existingPaymentIntent.id
            }
          });
        }
      } catch (error) {
        console.error('Error retrieving existing payment intent:', error);
        // 继续创建新的支付意图
      }
    }

    // 获取或创建Stripe客户
    const stripeCustomer = await getOrCreateStripeCustomer(
      user.email,
      user.name || undefined,
      {
        userId: user._id.toString(),
        orderId: order._id.toString()
      }
    );

    // 创建支付意图
    const paymentIntent = await createPaymentIntent(
      order.amount,
      'usd',
      {
        orderId: order._id.toString(),
        userId: user._id.toString(),
        toolId: order.toolId.toString(),
        productType: 'priority_launch'
      }
    );

    // 更新订单，保存支付意图ID和客户ID
    order.stripePaymentIntentId = paymentIntent.id;
    order.stripeCustomerId = stripeCustomer.id;
    await order.save();

    return NextResponse.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        amount: order.amount,
        currency: 'cny'
      }
    });

  } catch (error) {
    console.error('Error creating payment intent:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'payment.create_intent_failed') },
      { status: 500 }
    );
  }
}
